import 'package:http/http.dart' as http;
import 'package:html/parser.dart' as parser;

class WeatherScraper {
  static const String baseUrl = 'https://www.accuweather.com';

  Future<Map<String, dynamic>> getWeatherData(String location) async {
    try {
      // First, search for the location
      final searchUrl = '$baseUrl/en/search-locations?query=$location';
      final searchResponse = await http.get(Uri.parse(searchUrl));
      
      if (searchResponse.statusCode != 200) {
        throw Exception('Failed to search location');
      }

      final searchDoc = parser.parse(searchResponse.body);
      final locationLink = searchDoc.querySelector('.search-results a');
      
      if (locationLink == null) {
        throw Exception('Location not found');
      }

      final locationUrl = '$baseUrl${locationLink.attributes['href']}';
      final weatherResponse = await http.get(Uri.parse(locationUrl));
      
      if (weatherResponse.statusCode != 200) {
        throw Exception('Failed to get weather data');
      }

      final weatherDoc = parser.parse(weatherResponse.body);
      
      // Extract weather data
      final temperature = weatherDoc.querySelector('.temp')?.text ?? 'N/A';
      final condition = weatherDoc.querySelector('.phrase')?.text ?? 'N/A';
      final humidity = weatherDoc.querySelector('.humidity')?.text ?? 'N/A';
      final wind = weatherDoc.querySelector('.wind')?.text ?? 'N/A';
      final feelsLike = weatherDoc.querySelector('.real-feel')?.text ?? 'N/A';
      final uvIndex = weatherDoc.querySelector('.uv-index')?.text ?? 'N/A';

      return {
        'temperature': temperature,
        'condition': condition,
        'location': location,
        'humidity': humidity,
        'windSpeed': wind,
        'feelsLike': feelsLike,
        'uvIndex': uvIndex,
      };
    } catch (e) {
      throw Exception('Error: ${e.toString()}');
    }
  }
} 