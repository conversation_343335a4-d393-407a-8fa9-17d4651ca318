import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../core/constants/app_text_styles.dart';
import '../../core/widgets/custom_app_bar.dart';
import '../../core/widgets/gradient_button.dart';
import '../../providers/theme_provider.dart';
import 'widgets/color_picker.dart';
import 'widgets/live_preview.dart';

class ThemeStudioScreen extends StatelessWidget {
  const ThemeStudioScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final themeProvider = Provider.of<ThemeProvider>(context);

    return Scaffold(
      backgroundColor: themeProvider.backgroundColor,
      appBar: const CustomAppBar(
        title: 'Theme Studio',
        showBackButton: true,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Live Preview
            const LivePreview(),
            const SizedBox(height: 32),

            // Color Picker Section
            Text('Clock Colors', style: AppTextStyles.heading3),
            const SizedBox(height: 16),
            ColorPicker(
              title: 'Background',
              color: themeProvider.clockBackgroundColor,
              onColorChanged: themeProvider.setClockBackgroundColor,
            ),
            const SizedBox(height: 8),
            ColorPicker(
              title: 'Hands',
              color: themeProvider.clockHandsColor,
              onColorChanged: themeProvider.setClockHandsColor,
            ),
            const SizedBox(height: 8),
            ColorPicker(
              title: 'Numbers',
              color: themeProvider.clockNumbersColor,
              onColorChanged: themeProvider.setClockNumbersColor,
            ),
            const SizedBox(height: 8),
            ColorPicker(
              title: 'Ticks',
              color: themeProvider.clockTicksColor,
              onColorChanged: themeProvider.setClockTicksColor,
            ),
            const SizedBox(height: 32),

            // App Colors Section
            Text('App Colors', style: AppTextStyles.heading3),
            const SizedBox(height: 16),
            ColorPicker(
              title: 'Primary',
              color: themeProvider.primaryColor,
              onColorChanged: themeProvider.setPrimaryColor,
            ),
            const SizedBox(height: 8),
            ColorPicker(
              title: 'Secondary',
              color: themeProvider.secondaryColor,
              onColorChanged: themeProvider.setSecondaryColor,
            ),
            const SizedBox(height: 8),
            ColorPicker(
              title: 'Background',
              color: themeProvider.backgroundColor,
              onColorChanged: themeProvider.setBackgroundColor,
            ),
            const SizedBox(height: 8),
            ColorPicker(
              title: 'Surface',
              color: themeProvider.surfaceColor,
              onColorChanged: themeProvider.setSurfaceColor,
            ),
            const SizedBox(height: 32),

            // Action Buttons
            Row(
              children: [
                Expanded(
                  child: GradientButton(
                    text: 'Reset to Default',
                    onPressed: themeProvider.resetToDefault,
                    isFullWidth: true,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: GradientButton(
                    text: 'Save Theme',
                    onPressed: () {
                      // TODO: Save theme to Firestore
                    },
                    isFullWidth: true,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
} 