import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';

class AppTextStyles {
  // Headings
  static TextStyle get heading1 => GoogleFonts.roboto(
        fontSize: 32,
        fontWeight: FontWeight.bold,
        color: Colors.white,
      );

  static TextStyle get heading2 => GoogleFonts.roboto(
        fontSize: 24,
        fontWeight: FontWeight.bold,
        color: Colors.white,
      );

  static TextStyle get heading3 => GoogleFonts.roboto(
        fontSize: 20,
        fontWeight: FontWeight.w600,
        color: Colors.white,
      );

  // Body Text
  static TextStyle get bodyLarge => GoogleFonts.roboto(
        fontSize: 16,
        color: Colors.white,
      );

  static TextStyle get bodyMedium => GoogleFonts.roboto(
        fontSize: 14,
        color: Colors.white,
      );

  static TextStyle get bodySmall => GoogleFonts.roboto(
        fontSize: 12,
        color: Colors.white70,
      );

  // Clock Text
  static TextStyle get clockTime => GoogleFonts.robotoMono(
        fontSize: 48,
        fontWeight: FontWeight.w300,
        color: Colors.white,
      );

  static TextStyle get clockDate => GoogleFonts.roboto(
        fontSize: 16,
        color: Colors.white70,
      );
} 