import 'package:flutter/material.dart';

class AppColors {
  // Primary Colors - Material Design 3 inspired
  static const Color primary = Color(0xFF6750A4);
  static const Color primaryVariant = Color(0xFF4F378B);
  static const Color secondary = Color(0xFF03DAC6);
  static const Color secondaryVariant = Color(0xFF018786);
  static const Color tertiary = Color(0xFFEFB8C8);

  // Background Colors
  static const Color background = Color(0xFF121212);
  static const Color surface = Color(0xFF1E1E1E);
  static const Color surfaceVariant = Color(0xFF49454F);
  static const Color error = Color(0xFFCF6679);

  // Text Colors
  static const Color onPrimary = Colors.white;
  static const Color onSecondary = Colors.black;
  static const Color onBackground = Colors.white;
  static const Color onSurface = Colors.white;
  static const Color onError = Colors.black;

  // Clock Face Colors
  static const Color clockBackground = Color(0xFF2C2C2C);
  static const Color clockHands = Colors.white;
  static const Color clockNumbers = Colors.white70;
  static const Color clockTicks = Colors.white54;

  // Material Design 3 Theme Colors
  static const MaterialColor materialPurple = MaterialColor(
    0xFF6750A4,
    <int, Color>{
      50: Color(0xFFF5F0FF),
      100: Color(0xFFE9DDFF),
      200: Color(0xFFD0BCFF),
      300: Color(0xFFB69DF8),
      400: Color(0xFF9A82DB),
      500: Color(0xFF6750A4),
      600: Color(0xFF5B4497),
      700: Color(0xFF4F378B),
      800: Color(0xFF432B7E),
      900: Color(0xFF381E72),
    },
  );

  // Modern Theme Colors
  static const Color modernDarkBackground = Color(0xFF1C1B1F);
  static const Color modernDarkSurface = Color(0xFF2B2930);
  static const Color modernLightBackground = Color(0xFFFFFBFE);
  static const Color modernLightSurface = Color(0xFFF7F2FA);

  // Neon Theme Colors
  static const Color neonBackground = Color(0xFF0A0A0A);
  static const Color neonAccent = Color(0xFF00FFFF);
  static const Color neonPink = Color(0xFFFF00FF);
  static const Color neonGreen = Color(0xFF00FF00);

  // Minimal Theme Colors
  static const Color minimalBackground = Color(0xFFFFFFFF);
  static const Color minimalSurface = Color(0xFFF5F5F5);
  static const Color minimalAccent = Color(0xFF000000);

  // Amoled Theme Colors
  static const Color amoledBackground = Color(0xFF000000);
  static const Color amoledSurface = Color(0xFF121212);
  static const Color amoledAccent = Color(0xFFFFFFFF);
}