import 'package:flutter/material.dart';
import '../core/constants/app_colors.dart';

class ThemeProvider extends ChangeNotifier {
  // Theme options
  static const String themeKey = 'selected_theme';
  static const String colorKey = 'selected_color';

  // Available themes
  final List<String> _availableThemes = [
    'Dark',
    'Light',
    'Amoled',
    'Neon',
    'Minimal',
    'Vaporwave',
    'Retrowave',
    'Pastel',
    'Modern Minimal',
    'Sunrise',
    'Night Sky',
  ];

  // Available colors
  final List<Color> _availableColors = [
    Colors.blue,
    Colors.purple,
    Colors.pink,
    Colors.red,
    Colors.orange,
    Colors.amber,
    Colors.green,
    Colors.teal,
    Colors.cyan,
    Colors.indigo,
  ];

  // Current theme selection
  String _selectedTheme = 'Dark';

  Color _primaryColor = AppColors.primary;
  Color _secondaryColor = AppColors.secondary;
  Color _backgroundColor = Colors.black;
  Color _surfaceColor = AppColors.surface;
  Color _clockBackgroundColor = AppColors.clockBackground;
  Color _clockHandsColor = AppColors.clockHands;
  Color _clockNumbersColor = AppColors.clockNumbers;
  Color _clockTicksColor = AppColors.clockTicks;
  bool _showSeconds = true;
  bool _showDate = true;
  bool _showWeather = true;
  bool _is24HourFormat = false;
  double _glowIntensity = 0.5;
  bool _useManualLocation = false;
  String _manualLocation = '';

  // Constructor
  ThemeProvider() {
    // Initialize with default theme
  }

  // Getters
  String get selectedTheme => _selectedTheme;
  List<String> get availableThemes => _availableThemes;
  List<Color> get availableColors => _availableColors;

  Color get primaryColor => _primaryColor;
  Color get secondaryColor => _secondaryColor;
  Color get backgroundColor => _getBackgroundColor();
  Color get surfaceColor => _surfaceColor;
  Color get clockBackgroundColor => _clockBackgroundColor;
  Color get clockHandsColor => _clockHandsColor;
  Color get clockNumbersColor => _clockNumbersColor;
  Color get clockTicksColor => _clockTicksColor;
  bool get showSeconds => _showSeconds;
  bool get showDate => _showDate;
  bool get showWeather => _showWeather;
  bool get is24HourFormat => _is24HourFormat;
  double get glowIntensity => _glowIntensity;
  bool get useManualLocation => _useManualLocation;
  String get manualLocation => _manualLocation;

  // Text color based on theme
  Color get textColor {
    switch (_selectedTheme) {
      case 'Light':
      case 'Minimal':
      case 'Modern Minimal':
      case 'Pastel':
        return Colors.black;
      case 'Dark':
      case 'Amoled':
      case 'Neon':
      case 'Vaporwave':
      case 'Retrowave':
      case 'Sunrise':
      case 'Night Sky':
        return Colors.white;
      default:
        return Colors.white;
    }
  }

  // Secondary text color
  Color get secondaryTextColor {
    switch (_selectedTheme) {
      case 'Light':
      case 'Minimal':
      case 'Modern Minimal':
      case 'Pastel':
        return Colors.black54;
      case 'Dark':
      case 'Amoled':
      case 'Neon':
      case 'Vaporwave':
      case 'Retrowave':
      case 'Sunrise':
      case 'Night Sky':
        return Colors.white70;
      default:
        return Colors.white70;
    }
  }

  // Get background color based on theme
  Color _getBackgroundColor() {
    switch (_selectedTheme) {
      case 'Light':
        return AppColors.modernLightBackground;
      case 'Dark':
        return AppColors.modernDarkBackground;
      case 'Amoled':
        return AppColors.amoledBackground;
      case 'Neon':
        return AppColors.neonBackground;
      case 'Minimal':
        return AppColors.minimalBackground;
      case 'Vaporwave':
        return const Color(0xFF0093E9); // Vaporwave blue
      case 'Retrowave':
        return const Color(0xFF0F0F1B); // Deep blue-black
      case 'Pastel':
        return const Color(0xFFA6C0FE); // Pastel blue
      case 'Modern Minimal':
        return Colors.white;
      case 'Sunrise':
        return const Color(0xFFFF512F); // Sunset orange
      case 'Night Sky':
        return const Color(0xFF0F2027); // Deep blue-black
      default:
        return _backgroundColor;
    }
  }

  // Get gradient based on theme
  Gradient get backgroundGradient {
    switch (_selectedTheme) {
      case 'Light':
        return const LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            AppColors.modernLightBackground,
            AppColors.modernLightSurface,
          ],
          stops: [0.0, 1.0],
        );
      case 'Dark':
        return const LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            AppColors.modernDarkBackground,
            AppColors.modernDarkSurface,
          ],
          stops: [0.0, 1.0],
        );
      case 'Amoled':
        return const LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [
            AppColors.amoledBackground,
            AppColors.amoledSurface,
          ],
          stops: [0.0, 1.0],
        );
      case 'Neon':
        return const LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            AppColors.neonBackground,
            Color(0xFF0A0A14), // Darker version of neon background
            Color(0xFF0A0A1E), // Deep blue-black
          ],
          stops: [0.0, 0.5, 1.0],
        );
      case 'Minimal':
        return const LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [
            AppColors.minimalBackground,
            AppColors.minimalSurface,
          ],
          stops: [0.0, 1.0],
        );
      case 'Vaporwave':
        return const LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            Color(0xFF0093E9), // Vaporwave blue
            Color(0xFF80D0C7), // Light cyan
          ],
          stops: [0.0, 1.0],
        );
      case 'Retrowave':
        return const LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [
            Color(0xFF0F0F1B), // Deep blue-black
            Color(0xFF2E1A47), // Dark purple
            Color(0xFF541388), // Retrowave purple
          ],
          stops: [0.0, 0.5, 1.0],
        );
      case 'Pastel':
        return const LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            Color(0xFFA6C0FE), // Pastel blue
            Color(0xFFF68084), // Pastel pink
          ],
          stops: [0.0, 1.0],
        );
      case 'Modern Minimal':
        return const LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [
            Color(0xFFFFFFFF), // White
            Color(0xFFF5F5F5), // Light gray
          ],
          stops: [0.0, 1.0],
        );
      case 'Sunrise':
        return const LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [
            Color(0xFFFF512F), // Sunset orange
            Color(0xFFF09819), // Golden yellow
          ],
          stops: [0.0, 1.0],
        );
      case 'Night Sky':
        return const LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [
            Color(0xFF0F2027), // Deep blue-black
            Color(0xFF203A43), // Dark blue
            Color(0xFF2C5364), // Navy blue
          ],
          stops: [0.0, 0.5, 1.0],
        );
      default:
        return const LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            AppColors.modernDarkBackground,
            AppColors.modernDarkSurface,
          ],
          stops: [0.0, 1.0],
        );
    }
  }

  // Setters
  void setPrimaryColor(Color color) {
    _primaryColor = color;
    notifyListeners();
  }

  void setSecondaryColor(Color color) {
    _secondaryColor = color;
    notifyListeners();
  }

  void setBackgroundColor(Color color) {
    _backgroundColor = color;
    notifyListeners();
  }

  void setSurfaceColor(Color color) {
    _surfaceColor = color;
    notifyListeners();
  }

  void setClockBackgroundColor(Color color) {
    _clockBackgroundColor = color;
    notifyListeners();
  }

  void setClockHandsColor(Color color) {
    _clockHandsColor = color;
    notifyListeners();
  }

  void setClockNumbersColor(Color color) {
    _clockNumbersColor = color;
    notifyListeners();
  }

  void setClockTicksColor(Color color) {
    _clockTicksColor = color;
    notifyListeners();
  }

  void setShowSeconds(bool value) {
    _showSeconds = value;
    notifyListeners();
  }

  void setShowDate(bool value) {
    _showDate = value;
    notifyListeners();
  }

  void setShowWeather(bool value) {
    _showWeather = value;
    notifyListeners();
  }

  void set24HourFormat(bool value) {
    _is24HourFormat = value;
    notifyListeners();
  }

  void setGlowIntensity(double value) {
    _glowIntensity = value;
    notifyListeners();
  }

  void setUseManualLocation(bool value) {
    _useManualLocation = value;
    notifyListeners();
  }

  void setManualLocation(String value) {
    _manualLocation = value;
    notifyListeners();
  }

  // Set theme
  void setTheme(String theme) {
    if (_availableThemes.contains(theme)) {
      _selectedTheme = theme;
      notifyListeners();
    }
  }

  // Reset to default theme
  void resetToDefault() {
    _primaryColor = AppColors.primary;
    _secondaryColor = AppColors.secondary;
    _backgroundColor = Colors.black;
    _surfaceColor = AppColors.surface;
    _clockBackgroundColor = AppColors.clockBackground;
    _clockHandsColor = AppColors.clockHands;
    _clockNumbersColor = AppColors.clockNumbers;
    _clockTicksColor = AppColors.clockTicks;
    _showSeconds = true;
    _showDate = true;
    _showWeather = true;
    _is24HourFormat = false;
    _glowIntensity = 0.5;
    _useManualLocation = false;
    _manualLocation = '';
    _selectedTheme = 'Dark';
    notifyListeners();
  }
}