
class ThemeModel {
  final String id;
  final String name;
  final String description;
  final String creatorId;
  final String creatorName;
  final double price;
  final String? previewImageUrl;
  final DateTime createdAt;
  final DateTime updatedAt;
  final Map<String, dynamic> colors;
  final bool isPremium;
  final int downloads;
  final double rating;
  final int ratingCount;

  ThemeModel({
    required this.id,
    required this.name,
    required this.description,
    required this.creatorId,
    required this.creatorName,
    required this.price,
    this.previewImageUrl,
    required this.createdAt,
    required this.updatedAt,
    required this.colors,
    this.isPremium = false,
    this.downloads = 0,
    this.rating = 0.0,
    this.ratingCount = 0,
  });

  // Convert to Map for Firestore
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'creatorId': creatorId,
      'creatorName': creatorName,
      'price': price,
      'previewImageUrl': previewImageUrl,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
      'colors': colors,
      'isPremium': isPremium,
      'downloads': downloads,
      'rating': rating,
      'ratingCount': ratingCount,
    };
  }

  // Create from Firestore Map
  factory ThemeModel.fromMap(Map<String, dynamic> map) {
    return ThemeModel(
      id: map['id'] ?? '',
      name: map['name'] ?? '',
      description: map['description'] ?? '',
      creatorId: map['creatorId'] ?? '',
      creatorName: map['creatorName'] ?? '',
      price: (map['price'] ?? 0.0).toDouble(),
      previewImageUrl: map['previewImageUrl'],
      createdAt: DateTime.parse(map['createdAt'] ?? DateTime.now().toIso8601String()),
      updatedAt: DateTime.parse(map['updatedAt'] ?? DateTime.now().toIso8601String()),
      colors: Map<String, dynamic>.from(map['colors'] ?? {}),
      isPremium: map['isPremium'] ?? false,
      downloads: map['downloads'] ?? 0,
      rating: (map['rating'] ?? 0.0).toDouble(),
      ratingCount: map['ratingCount'] ?? 0,
    );
  }

  // Create a copy with updated fields
  ThemeModel copyWith({
    String? id,
    String? name,
    String? description,
    String? creatorId,
    String? creatorName,
    double? price,
    String? previewImageUrl,
    DateTime? createdAt,
    DateTime? updatedAt,
    Map<String, dynamic>? colors,
    bool? isPremium,
    int? downloads,
    double? rating,
    int? ratingCount,
  }) {
    return ThemeModel(
      id: id ?? this.id,
      name: name ?? this.name,
      description: description ?? this.description,
      creatorId: creatorId ?? this.creatorId,
      creatorName: creatorName ?? this.creatorName,
      price: price ?? this.price,
      previewImageUrl: previewImageUrl ?? this.previewImageUrl,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      colors: colors ?? this.colors,
      isPremium: isPremium ?? this.isPremium,
      downloads: downloads ?? this.downloads,
      rating: rating ?? this.rating,
      ratingCount: ratingCount ?? this.ratingCount,
    );
  }
} 