import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../core/constants/app_colors.dart';
import '../../core/constants/app_text_styles.dart';
import '../../core/widgets/custom_app_bar.dart';
import '../../core/widgets/gradient_button.dart';
import '../../models/theme_model.dart';
import '../../providers/auth_provider.dart';
import 'marketplace_service.dart';

class MarketplaceScreen extends StatefulWidget {
  const MarketplaceScreen({super.key});

  @override
  State<MarketplaceScreen> createState() => _MarketplaceScreenState();
}

class _MarketplaceScreenState extends State<MarketplaceScreen> {
  final MarketplaceService _marketplaceService = MarketplaceService();
  List<ThemeModel> _themes = [];
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadThemes();
  }

  Future<void> _loadThemes() async {
    try {
      final themes = await _marketplaceService.getThemes();
      setState(() {
        _themes = themes;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      if (mounted) {
        context.showSnackBar('Failed to load themes', isError: true);
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final authProvider = Provider.of<AuthProvider>(context);

    return Scaffold(
      backgroundColor: AppColors.background,
      appBar: const CustomAppBar(
        title: 'Marketplace',
        showBackButton: true,
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : RefreshIndicator(
              onRefresh: _loadThemes,
              child: ListView.builder(
                padding: const EdgeInsets.all(16),
                itemCount: _themes.length,
                itemBuilder: (context, index) {
                  final theme = _themes[index];
                  return _ThemeCard(
                    theme: theme,
                    onPurchase: () async {
                      if (!authProvider.isAuthenticated) {
                        context.showSnackBar('Please sign in to purchase themes');
                        return;
                      }

                      try {
                        await _marketplaceService.purchaseTheme(
                          theme.id,
                          authProvider.user!.id,
                        );
                        if (mounted) {
                          context.showSnackBar('Theme purchased successfully!');
                        }
                      } catch (e) {
                        if (mounted) {
                          context.showSnackBar('Failed to purchase theme', isError: true);
                        }
                      }
                    },
                  );
                },
              ),
            ),
    );
  }
}

class _ThemeCard extends StatelessWidget {
  final ThemeModel theme;
  final VoidCallback onPurchase;

  const _ThemeCard({
    required this.theme,
    required this.onPurchase,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      color: AppColors.surface,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Preview Image
          if (theme.previewImageUrl != null)
            ClipRRect(
              borderRadius: const BorderRadius.vertical(top: Radius.circular(8)),
              child: Image.network(
                theme.previewImageUrl!,
                height: 200,
                width: double.infinity,
                fit: BoxFit.cover,
              ),
            ),

          // Theme Info
          Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(theme.name, style: AppTextStyles.heading3),
                const SizedBox(height: 8),
                Text(
                  theme.description,
                  style: AppTextStyles.bodyMedium,
                ),
                const SizedBox(height: 16),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      'By ${theme.creatorName}',
                      style: AppTextStyles.bodySmall,
                    ),
                    Text(
                      '\$${theme.price.toStringAsFixed(2)}',
                      style: AppTextStyles.heading3,
                    ),
                  ],
                ),
                const SizedBox(height: 16),
                GradientButton(
                  text: 'Purchase',
                  onPressed: onPurchase,
                  isFullWidth: true,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
} 