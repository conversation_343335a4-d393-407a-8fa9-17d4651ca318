import 'dart:async';
import 'dart:math' as math;
import 'package:flutter/material.dart';

class FlipClock extends StatefulWidget {
  final DateTime time;
  final bool isLightTheme;
  final bool showSeconds;
  final bool is24HourFormat;

  const FlipClock({
    Key? key,
    required this.time,
    this.isLightTheme = false,
    this.showSeconds = true,
    this.is24HourFormat = false,
  }) : super(key: key);

  @override
  State<FlipClock> createState() => _FlipClockState();
}

class _FlipClockState extends State<FlipClock> with TickerProviderStateMixin {
  late AnimationController _hourTensController;
  late AnimationController _hourOnesController;
  late AnimationController _minuteTensController;
  late AnimationController _minuteOnesController;
  late AnimationController _secondTensController;
  late AnimationController _secondOnesController;
  late AnimationController _periodController;

  late Animation<double> _hourTensAnimation;
  late Animation<double> _hourOnesAnimation;
  late Animation<double> _minuteTensAnimation;
  late Animation<double> _minuteOnesAnimation;
  late Animation<double> _secondTensAnimation;
  late Animation<double> _secondOnesAnimation;
  late Animation<double> _periodAnimation;

  late int _hourTens = 0;
  late int _hourOnes = 0;
  late int _minuteTens = 0;
  late int _minuteOnes = 0;
  late int _secondTens = 0;
  late int _secondOnes = 0;
  late String _period = 'AM';

  late int _prevHourTens = 0;
  late int _prevHourOnes = 0;
  late int _prevMinuteTens = 0;
  late int _prevMinuteOnes = 0;
  late int _prevSecondTens = 0;
  late int _prevSecondOnes = 0;
  late String _prevPeriod = 'AM';

  @override
  void initState() {
    super.initState();

    _hourTensController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 800),
    );
    _hourOnesController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 800),
    );
    _minuteTensController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 800),
    );
    _minuteOnesController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 800),
    );
    _secondTensController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 800),
    );
    _secondOnesController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 800),
    );
    _periodController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 800),
    );

    // Use a custom curve that starts fast and slows down at the end
    const Curve customFlipCurve = Curves.easeOutQuint;

    _hourTensAnimation = Tween<double>(begin: 0, end: math.pi).animate(
      CurvedAnimation(parent: _hourTensController, curve: customFlipCurve),
    );
    _hourOnesAnimation = Tween<double>(begin: 0, end: math.pi).animate(
      CurvedAnimation(parent: _hourOnesController, curve: customFlipCurve),
    );
    _minuteTensAnimation = Tween<double>(begin: 0, end: math.pi).animate(
      CurvedAnimation(parent: _minuteTensController, curve: customFlipCurve),
    );
    _minuteOnesAnimation = Tween<double>(begin: 0, end: math.pi).animate(
      CurvedAnimation(parent: _minuteOnesController, curve: customFlipCurve),
    );
    _secondTensAnimation = Tween<double>(begin: 0, end: math.pi).animate(
      CurvedAnimation(parent: _secondTensController, curve: customFlipCurve),
    );
    _secondOnesAnimation = Tween<double>(begin: 0, end: math.pi).animate(
      CurvedAnimation(parent: _secondOnesController, curve: customFlipCurve),
    );
    _periodAnimation = Tween<double>(begin: 0, end: math.pi).animate(
      CurvedAnimation(parent: _periodController, curve: customFlipCurve),
    );

    _updateTime();
  }

  @override
  void didUpdateWidget(FlipClock oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.time.second != widget.time.second) {
      _updateTime();
    }
  }

  void _updateTime() {
    // Store previous values
    _prevHourTens = _hourTens;
    _prevHourOnes = _hourOnes;
    _prevMinuteTens = _minuteTens;
    _prevMinuteOnes = _minuteOnes;
    _prevSecondTens = _secondTens;
    _prevSecondOnes = _secondOnes;
    _prevPeriod = _period;

    // Get new time values
    int hour = widget.time.hour;
    final minute = widget.time.minute;
    final second = widget.time.second;

    // Format hour based on 12/24 hour setting
    if (!widget.is24HourFormat) {
      _period = hour >= 12 ? 'PM' : 'AM';
      hour = hour % 12;
      if (hour == 0) hour = 12;
    }

    // Split digits
    _hourTens = hour ~/ 10;
    _hourOnes = hour % 10;
    _minuteTens = minute ~/ 10;
    _minuteOnes = minute % 10;
    _secondTens = second ~/ 10;
    _secondOnes = second % 10;

    // Animate changing digits with slight delay between them for better visual effect
    Future.microtask(() {
      if (_hourTens != _prevHourTens) {
        _hourTensController.reset();
        _hourTensController.forward();
      }

      if (_hourOnes != _prevHourOnes) {
        _hourOnesController.reset();
        Future.delayed(const Duration(milliseconds: 50), () {
          if (mounted) _hourOnesController.forward();
        });
      }

      if (_minuteTens != _prevMinuteTens) {
        _minuteTensController.reset();
        Future.delayed(const Duration(milliseconds: 100), () {
          if (mounted) _minuteTensController.forward();
        });
      }

      if (_minuteOnes != _prevMinuteOnes) {
        _minuteOnesController.reset();
        Future.delayed(const Duration(milliseconds: 150), () {
          if (mounted) _minuteOnesController.forward();
        });
      }

      if (widget.showSeconds) {
        if (_secondTens != _prevSecondTens) {
          _secondTensController.reset();
          Future.delayed(const Duration(milliseconds: 200), () {
            if (mounted) _secondTensController.forward();
          });
        }

        if (_secondOnes != _prevSecondOnes) {
          _secondOnesController.reset();
          Future.delayed(const Duration(milliseconds: 250), () {
            if (mounted) _secondOnesController.forward();
          });
        }
      }

      if (_period != _prevPeriod) {
        _periodController.reset();
        Future.delayed(const Duration(milliseconds: 300), () {
          if (mounted) _periodController.forward();
        });
      }
    });
  }

  @override
  void dispose() {
    _hourTensController.dispose();
    _hourOnesController.dispose();
    _minuteTensController.dispose();
    _minuteOnesController.dispose();
    _secondTensController.dispose();
    _secondOnesController.dispose();
    _periodController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        // Hours
        _buildFlipDigit(_hourTens, _prevHourTens, _hourTensAnimation),
        _buildFlipDigit(_hourOnes, _prevHourOnes, _hourOnesAnimation),

        // Separator
        _buildSeparator(),

        // Minutes
        _buildFlipDigit(_minuteTens, _prevMinuteTens, _minuteTensAnimation),
        _buildFlipDigit(_minuteOnes, _prevMinuteOnes, _minuteOnesAnimation),

        // Seconds (optional)
        if (widget.showSeconds) ...[
          _buildSeparator(),
          _buildFlipDigit(_secondTens, _prevSecondTens, _secondTensAnimation),
          _buildFlipDigit(_secondOnes, _prevSecondOnes, _secondOnesAnimation),
        ],

        // AM/PM (for 12-hour format)
        if (!widget.is24HourFormat)
          _buildPeriodFlip(_period, _prevPeriod, _periodAnimation),
      ],
    );
  }

  Widget _buildSeparator() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 6),
      child: Text(
        ':',
        style: TextStyle(
          fontSize: 80,
          fontWeight: FontWeight.bold,
          color: widget.isLightTheme ? Colors.black87 : Colors.white,
        ),
      ),
    );
  }

  Widget _buildFlipDigit(int digit, int prevDigit, Animation<double> animation) {
    return Container(
      width: 100,  // Increased from 80
      height: 150, // Increased from 120
      margin: const EdgeInsets.symmetric(horizontal: 4),
      child: AnimatedBuilder(
        animation: animation,
        builder: (context, child) {
          return _buildFlipPanel(
            digit: digit,
            prevDigit: prevDigit,
            animation: animation,
          );
        },
      ),
    );
  }

  Widget _buildPeriodFlip(String period, String prevPeriod, Animation<double> animation) {
    return Container(
      width: 60,
      height: 40,
      margin: const EdgeInsets.only(left: 8),
      child: AnimatedBuilder(
        animation: animation,
        builder: (context, child) {
          return _buildPeriodPanel(
            period: period,
            prevPeriod: prevPeriod,
            animation: animation,
          );
        },
      ),
    );
  }

  Widget _buildFlipPanel({
    required int digit,
    required int prevDigit,
    required Animation<double> animation,
  }) {
    // In a real flip clock:
    // - Top half shows current digit
    // - Bottom half shows next digit (which is the current digit when not flipping)
    // - When flipping, the top half flips down to reveal the next digit

    final isFlipping = animation.value > 0 && animation.value < math.pi;
    final isHalfFlipped = animation.value >= math.pi / 2;

    // Colors
    final Color backgroundColor = widget.isLightTheme ? Colors.grey.shade200 : Colors.grey.shade900;
    final Color textColor = widget.isLightTheme ? Colors.black87 : Colors.white;
    final Color shadowColor = Colors.black.withAlpha(51);

    // Height of each half
    const double topHalfHeight = 75.0;
    const double dividerHeight = 1.0;
    const double bottomHalfHeight = 74.0; // 75 - 1 for divider
    const double totalHeight = topHalfHeight + dividerHeight + bottomHalfHeight;

    return SizedBox(
      height: totalHeight,
      child: Stack(
        children: [
          // Background container with shadow
          Container(
            decoration: BoxDecoration(
              color: backgroundColor,
              borderRadius: BorderRadius.circular(8),
              boxShadow: [
                BoxShadow(
                  color: shadowColor,
                  blurRadius: 4,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
          ),

          // Static bottom half (always shows the next digit)
          Positioned(
            top: topHalfHeight + dividerHeight,
            left: 0,
            right: 0,
            height: bottomHalfHeight,
            child: Container(
              decoration: BoxDecoration(
                color: backgroundColor,
                borderRadius: const BorderRadius.vertical(bottom: Radius.circular(8)),
              ),
              child: ClipRRect(
                borderRadius: const BorderRadius.vertical(bottom: Radius.circular(8)),
                child: Center(
                  child: Text(
                    digit.toString(),
                    style: TextStyle(
                      fontSize: 100,
                      fontWeight: FontWeight.bold,
                      color: textColor,
                      height: 1.1,
                    ),
                  ),
                ),
              ),
            ),
          ),

          // Dividing line
          Positioned(
            top: topHalfHeight,
            left: 0,
            right: 0,
            height: dividerHeight,
            child: Container(
              color: widget.isLightTheme
                  ? Colors.grey.shade400.withAlpha(80)
                  : Colors.grey.shade700.withAlpha(80),
            ),
          ),

          // Static top half (shows current digit when not flipping)
          if (!isFlipping)
            Positioned(
              top: 0,
              left: 0,
              right: 0,
              height: topHalfHeight,
              child: Container(
                decoration: BoxDecoration(
                  color: backgroundColor,
                  borderRadius: const BorderRadius.vertical(top: Radius.circular(8)),
                ),
                child: ClipRRect(
                  borderRadius: const BorderRadius.vertical(top: Radius.circular(8)),
                  child: Center(
                    child: Text(
                      prevDigit.toString(),
                      style: TextStyle(
                        fontSize: 100,
                        fontWeight: FontWeight.bold,
                        color: textColor,
                        height: 1.1,
                      ),
                    ),
                  ),
                ),
              ),
            ),

          // Flipping top half (first half of animation)
          if (isFlipping && !isHalfFlipped)
            Positioned(
              top: 0,
              left: 0,
              right: 0,
              height: topHalfHeight,
              child: Transform(
                alignment: Alignment.bottomCenter,
                transform: Matrix4.identity()
                  ..setEntry(3, 2, 0.001) // Perspective
                  ..rotateX(animation.value),
                child: Container(
                  decoration: BoxDecoration(
                    color: backgroundColor,
                    borderRadius: const BorderRadius.vertical(top: Radius.circular(8)),
                  ),
                  child: ClipRRect(
                    borderRadius: const BorderRadius.vertical(top: Radius.circular(8)),
                    child: Center(
                      child: Text(
                        prevDigit.toString(),
                        style: TextStyle(
                          fontSize: 100,
                          fontWeight: FontWeight.bold,
                          color: textColor,
                          height: 1.1,
                        ),
                      ),
                    ),
                  ),
                ),
              ),
            ),

          // Flipping bottom half (second half of animation)
          if (isFlipping && isHalfFlipped)
            Positioned(
              top: 0,
              left: 0,
              right: 0,
              height: topHalfHeight,
              child: Transform(
                alignment: Alignment.bottomCenter,
                transform: Matrix4.identity()
                  ..setEntry(3, 2, 0.001) // Perspective
                  ..rotateX(math.pi),
                child: Transform(
                  alignment: Alignment.topCenter,
                  transform: Matrix4.identity()
                    ..setEntry(3, 2, 0.001) // Perspective
                    ..rotateX(animation.value - math.pi),
                  child: Container(
                    decoration: BoxDecoration(
                      color: backgroundColor,
                      borderRadius: const BorderRadius.vertical(bottom: Radius.circular(8)),
                    ),
                    child: ClipRRect(
                      borderRadius: const BorderRadius.vertical(bottom: Radius.circular(8)),
                      child: Center(
                        child: Text(
                          digit.toString(),
                          style: TextStyle(
                            fontSize: 100,
                            fontWeight: FontWeight.bold,
                            color: textColor,
                            height: 1.1,
                          ),
                        ),
                      ),
                    ),
                  ),
                ),
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildPeriodPanel({
    required String period,
    required String prevPeriod,
    required Animation<double> animation,
  }) {
    // In a real flip clock:
    // - Top half shows current period
    // - Bottom half shows next period (which is the current period when not flipping)
    // - When flipping, the top half flips down to reveal the next period

    final isFlipping = animation.value > 0 && animation.value < math.pi;
    final isHalfFlipped = animation.value >= math.pi / 2;

    // Colors
    final Color backgroundColor = widget.isLightTheme ? Colors.grey.shade200 : Colors.grey.shade900;
    final Color textColor = widget.isLightTheme ? Colors.black87 : Colors.white;
    final Color shadowColor = Colors.black.withAlpha(51);

    // Height of each half
    const double topHalfHeight = 20.0;
    const double dividerHeight = 1.0;
    const double bottomHalfHeight = 19.0; // 20 - 1 for divider
    const double totalHeight = topHalfHeight + dividerHeight + bottomHalfHeight;

    return SizedBox(
      height: totalHeight,
      child: Stack(
        children: [
          // Background container with shadow
          Container(
            decoration: BoxDecoration(
              color: backgroundColor,
              borderRadius: BorderRadius.circular(8),
              boxShadow: [
                BoxShadow(
                  color: shadowColor,
                  blurRadius: 4,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
          ),

          // Static bottom half (always shows the next period)
          Positioned(
            top: topHalfHeight + dividerHeight,
            left: 0,
            right: 0,
            height: bottomHalfHeight,
            child: Container(
              decoration: BoxDecoration(
                color: backgroundColor,
                borderRadius: const BorderRadius.vertical(bottom: Radius.circular(8)),
              ),
              child: ClipRRect(
                borderRadius: const BorderRadius.vertical(bottom: Radius.circular(8)),
                child: Center(
                  child: Text(
                    period,
                    style: TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                      color: textColor,
                    ),
                  ),
                ),
              ),
            ),
          ),

          // Dividing line
          Positioned(
            top: topHalfHeight,
            left: 0,
            right: 0,
            height: dividerHeight,
            child: Container(
              color: widget.isLightTheme
                  ? Colors.grey.shade400.withAlpha(80)
                  : Colors.grey.shade700.withAlpha(80),
            ),
          ),

          // Static top half (shows current period when not flipping)
          if (!isFlipping)
            Positioned(
              top: 0,
              left: 0,
              right: 0,
              height: topHalfHeight,
              child: Container(
                decoration: BoxDecoration(
                  color: backgroundColor,
                  borderRadius: const BorderRadius.vertical(top: Radius.circular(8)),
                ),
                child: ClipRRect(
                  borderRadius: const BorderRadius.vertical(top: Radius.circular(8)),
                  child: Center(
                    child: Text(
                      prevPeriod,
                      style: TextStyle(
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                        color: textColor,
                      ),
                    ),
                  ),
                ),
              ),
            ),

          // Flipping top half (first half of animation)
          if (isFlipping && !isHalfFlipped)
            Positioned(
              top: 0,
              left: 0,
              right: 0,
              height: topHalfHeight,
              child: Transform(
                alignment: Alignment.bottomCenter,
                transform: Matrix4.identity()
                  ..setEntry(3, 2, 0.001) // Perspective
                  ..rotateX(animation.value),
                child: Container(
                  decoration: BoxDecoration(
                    color: backgroundColor,
                    borderRadius: const BorderRadius.vertical(top: Radius.circular(8)),
                  ),
                  child: ClipRRect(
                    borderRadius: const BorderRadius.vertical(top: Radius.circular(8)),
                    child: Center(
                      child: Text(
                        prevPeriod,
                        style: TextStyle(
                          fontSize: 20,
                          fontWeight: FontWeight.bold,
                          color: textColor,
                        ),
                      ),
                    ),
                  ),
                ),
              ),
            ),

          // Flipping bottom half (second half of animation)
          if (isFlipping && isHalfFlipped)
            Positioned(
              top: 0,
              left: 0,
              right: 0,
              height: topHalfHeight,
              child: Transform(
                alignment: Alignment.bottomCenter,
                transform: Matrix4.identity()
                  ..setEntry(3, 2, 0.001) // Perspective
                  ..rotateX(math.pi),
                child: Transform(
                  alignment: Alignment.topCenter,
                  transform: Matrix4.identity()
                    ..setEntry(3, 2, 0.001) // Perspective
                    ..rotateX(animation.value - math.pi),
                  child: Container(
                    decoration: BoxDecoration(
                      color: backgroundColor,
                      borderRadius: const BorderRadius.vertical(bottom: Radius.circular(8)),
                    ),
                    child: ClipRRect(
                      borderRadius: const BorderRadius.vertical(bottom: Radius.circular(8)),
                      child: Center(
                        child: Text(
                          period,
                          style: TextStyle(
                            fontSize: 20,
                            fontWeight: FontWeight.bold,
                            color: textColor,
                          ),
                        ),
                      ),
                    ),
                  ),
                ),
              ),
            ),
        ],
      ),
    );
  }
}
