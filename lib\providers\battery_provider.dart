import 'dart:async';
import 'package:flutter/material.dart';
import 'package:battery_plus/battery_plus.dart';

class BatteryProvider extends ChangeNotifier {
  final Battery _battery = Battery();
  int _batteryLevel = 100;
  BatteryState _batteryState = BatteryState.full;
  late Timer _timer;

  BatteryProvider() {
    _initBattery();
  }

  int get batteryLevel => _batteryLevel;
  BatteryState get batteryState => _batteryState;

  Future<void> _initBattery() async {
    // Get initial battery level
    _batteryLevel = await _battery.batteryLevel;
    _batteryState = await _battery.batteryState;
    notifyListeners();

    // Listen for battery state changes
    _battery.onBatteryStateChanged.listen((BatteryState state) {
      _batteryState = state;
      notifyListeners();
    });

    // Update battery level periodically
    _timer = Timer.periodic(const Duration(seconds: 30), (timer) async {
      final level = await _battery.batteryLevel;
      if (_batteryLevel != level) {
        _batteryLevel = level;
        notifyListeners();
      }
    });
  }

  IconData getBatteryIcon() {
    if (_batteryState == BatteryState.charging) {
      return Icons.battery_charging_full;
    }
    if (_batteryLevel >= 90) {
      return Icons.battery_full;
    } else if (_batteryLevel >= 70) {
      return Icons.battery_6_bar;
    } else if (_batteryLevel >= 50) {
      return Icons.battery_5_bar;
    } else if (_batteryLevel >= 30) {
      return Icons.battery_4_bar;
    } else if (_batteryLevel >= 20) {
      return Icons.battery_3_bar;
    } else if (_batteryLevel >= 10) {
      return Icons.battery_2_bar;
    } else if (_batteryLevel >= 5) {
      return Icons.battery_1_bar;
    } else {
      return Icons.battery_alert;
    }
  }

  Color getBatteryColor() {
    if (_batteryState == BatteryState.charging) {
      return Colors.green;
    }
    if (_batteryLevel > 50) {
      return Colors.green;
    } else if (_batteryLevel > 20) {
      return Colors.orange;
    } else {
      return Colors.red;
    }
  }

  String getBatteryText() {
    if (_batteryState == BatteryState.charging) {
      return 'Charging: $_batteryLevel%';
    } else {
      return '$_batteryLevel%';
    }
  }

  @override
  void dispose() {
    _timer.cancel();
    super.dispose();
  }
}
