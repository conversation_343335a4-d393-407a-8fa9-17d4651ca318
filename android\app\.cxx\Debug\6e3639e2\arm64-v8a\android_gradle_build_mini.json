{"buildFiles": ["C:\\flutter\\src\\flutter\\packages\\flutter_tools\\gradle\\src\\main\\groovy\\CMakeLists.txt"], "cleanCommandsComponents": [["C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "D:\\flutter apps\\custom_smart_clock\\android\\app\\.cxx\\Debug\\6e3639e2\\arm64-v8a", "clean"]], "buildTargetsCommandComponents": ["C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "D:\\flutter apps\\custom_smart_clock\\android\\app\\.cxx\\Debug\\6e3639e2\\arm64-v8a", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}}