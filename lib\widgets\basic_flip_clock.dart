import 'dart:async';
import 'package:flutter/material.dart';

class BasicFlipClock extends StatefulWidget {
  final bool showSeconds;
  final bool is24HourFormat;
  final Color digitColor;
  final Color backgroundColor;

  const BasicFlipClock({
    Key? key,
    this.showSeconds = true,
    this.is24HourFormat = false,
    this.digitColor = Colors.white,
    this.backgroundColor = const Color(0xFF1C1B1F),
  }) : super(key: key);

  @override
  State<BasicFlipClock> createState() => _BasicFlipClockState();
}

class _BasicFlipClockState extends State<BasicFlipClock> {
  late Timer _timer;
  late DateTime _currentTime;
  String _timeString = '';
  String _periodString = '';

  @override
  void initState() {
    super.initState();
    _updateTime();
    _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      setState(() {
        _updateTime();
      });
    });
  }

  @override
  void dispose() {
    _timer.cancel();
    super.dispose();
  }

  void _updateTime() {
    _currentTime = DateTime.now();
    
    // Format hour based on 12/24 hour setting
    int hour = _currentTime.hour;
    if (!widget.is24HourFormat) {
      _periodString = hour >= 12 ? 'PM' : 'AM';
      hour = hour % 12;
      if (hour == 0) hour = 12;
    } else {
      _periodString = '';
    }
    
    // Format time string
    final hourStr = hour.toString().padLeft(2, '0');
    final minuteStr = _currentTime.minute.toString().padLeft(2, '0');
    
    if (widget.showSeconds) {
      final secondStr = _currentTime.second.toString().padLeft(2, '0');
      _timeString = '$hourStr:$minuteStr:$secondStr';
    } else {
      _timeString = '$hourStr:$minuteStr';
    }
  }

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        // Main time display
        _buildTimeDisplay(),
        
        // AM/PM indicator (for 12-hour format)
        if (!widget.is24HourFormat) ...[
          const SizedBox(width: 16),
          _buildPeriodDisplay(),
        ],
      ],
    );
  }

  Widget _buildTimeDisplay() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 16),
      decoration: BoxDecoration(
        color: widget.backgroundColor,
        borderRadius: BorderRadius.circular(16),
        boxShadow: const [
          BoxShadow(
            color: Color(0x4D000000), // 0.3 opacity black
            blurRadius: 8,
            offset: Offset(0, 4),
          ),
        ],
      ),
      child: Text(
        _timeString,
        style: TextStyle(
          fontSize: 60,
          fontWeight: FontWeight.bold,
          color: widget.digitColor,
          letterSpacing: 2,
        ),
      ),
    );
  }

  Widget _buildPeriodDisplay() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      decoration: BoxDecoration(
        color: widget.backgroundColor,
        borderRadius: BorderRadius.circular(8),
        boxShadow: const [
          BoxShadow(
            color: Color(0x4D000000), // 0.3 opacity black
            blurRadius: 4,
            offset: Offset(0, 2),
          ),
        ],
      ),
      child: Text(
        _periodString,
        style: TextStyle(
          fontSize: 24,
          fontWeight: FontWeight.bold,
          color: widget.digitColor,
        ),
      ),
    );
  }
}
