import 'dart:async';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:intl/intl.dart';
import '../providers/battery_provider.dart';
import '../providers/theme_provider.dart';
import '../providers/clock_style_provider.dart';
import '../providers/font_provider.dart';
import '../core/constants/app_text_styles.dart';
import 'analog_clock.dart';
import 'binary_clock.dart';

class ClockPage extends StatefulWidget {
  final bool isLandscape;

  const ClockPage({
    Key? key,
    required this.isLandscape,
  }) : super(key: key);

  @override
  State<ClockPage> createState() => _ClockPageState();
}

class _ClockPageState extends State<ClockPage> {
  late DateTime _currentTime;
  late Timer _timer;

  @override
  void initState() {
    super.initState();
    _currentTime = DateTime.now();

    // Update time every second
    _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      setState(() {
        _currentTime = DateTime.now();
      });
    });
  }

  @override
  void dispose() {
    _timer.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final batteryProvider = Provider.of<BatteryProvider>(context);
    final themeProvider = Provider.of<ThemeProvider>(context);
    final clockStyleProvider = Provider.of<ClockStyleProvider>(context);
    final fontProvider = Provider.of<FontProvider>(context);

    // Update font based on theme and clock style
    fontProvider.updateFont(themeProvider.selectedTheme, clockStyleProvider.currentStyle);

    return widget.isLandscape
        ? _buildLandscapeLayout(batteryProvider, themeProvider, clockStyleProvider, fontProvider)
        : _buildPortraitLayout(batteryProvider, themeProvider, clockStyleProvider, fontProvider);
  }

  Widget _buildPortraitLayout(
    BatteryProvider batteryProvider,
    ThemeProvider themeProvider,
    ClockStyleProvider clockStyleProvider,
    FontProvider fontProvider,
  ) {
    // Standard layout for all clock styles
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 24.0, vertical: 32.0),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          // Date
          if (clockStyleProvider.showDate) ...[
            Text(
              clockStyleProvider.showDayOfWeek
                  ? DateFormat('EEEE, MMMM d, yyyy').format(_currentTime)
                  : DateFormat('MMMM d, yyyy').format(_currentTime),
              style: fontProvider.getDateStyle(
                themeProvider: themeProvider,
                fontSize: 18,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 24),
          ],

          // Clock display
          _buildClockDisplay(clockStyleProvider, themeProvider, fontProvider),

          const SizedBox(height: 40),

          // Battery info
          _buildBatteryInfo(batteryProvider, themeProvider, fontProvider),
        ],
      ),
    );
  }

  Widget _buildLandscapeLayout(
    BatteryProvider batteryProvider,
    ThemeProvider themeProvider,
    ClockStyleProvider clockStyleProvider,
    FontProvider fontProvider,
  ) {
    // Standard layout for all clock styles
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 32.0, vertical: 24.0),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        children: [
          // Left side - Clock
          Expanded(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                _buildClockDisplay(clockStyleProvider, themeProvider, fontProvider),
              ],
            ),
          ),

          // Right side - Date and Battery
          Expanded(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                // Date
                if (clockStyleProvider.showDate)
                  Text(
                    clockStyleProvider.showDayOfWeek
                        ? DateFormat('EEEE,\nMMMM d, yyyy').format(_currentTime)
                        : DateFormat('MMMM d, yyyy').format(_currentTime),
                    style: fontProvider.getDateStyle(
                      themeProvider: themeProvider,
                      fontSize: 18,
                    ),
                    textAlign: TextAlign.center,
                  ),
                const SizedBox(height: 40),

                // Battery info
                _buildBatteryInfo(batteryProvider, themeProvider, fontProvider),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildClockDisplay(
    ClockStyleProvider clockStyleProvider,
    ThemeProvider themeProvider,
    [FontProvider? fontProvider]
  ) {
    switch (clockStyleProvider.currentStyle) {
      case ClockStyle.digital:
        return _buildDigitalClock(
          showSeconds: false,
          themeProvider: themeProvider,
          clockStyleProvider: clockStyleProvider,
          fontProvider: fontProvider,
        );

      case ClockStyle.digitalWithSeconds:
        return _buildDigitalClock(
          showSeconds: true,
          themeProvider: themeProvider,
          clockStyleProvider: clockStyleProvider,
          fontProvider: fontProvider,
        );

      case ClockStyle.analogBasic:
        return _buildAnalogClock(
          style: AnalogClockStyle.basic,
          themeProvider: themeProvider,
          clockStyleProvider: clockStyleProvider,
          fontProvider: fontProvider,
        );

      case ClockStyle.analogModern:
        return _buildAnalogClock(
          style: AnalogClockStyle.modern,
          themeProvider: themeProvider,
          clockStyleProvider: clockStyleProvider,
          fontProvider: fontProvider,
        );

      case ClockStyle.analogMinimal:
        return _buildAnalogClock(
          style: AnalogClockStyle.minimal,
          themeProvider: themeProvider,
          clockStyleProvider: clockStyleProvider,
          fontProvider: fontProvider,
        );

      case ClockStyle.binaryClock:
        return _buildBinaryClock(
          themeProvider: themeProvider,
          clockStyleProvider: clockStyleProvider,
          fontProvider: fontProvider,
        );
    }
  }

  Widget _buildDigitalClock({
    required bool showSeconds,
    required ThemeProvider themeProvider,
    required ClockStyleProvider clockStyleProvider,
    FontProvider? fontProvider,
  }) {
    // Format time based on 12/24 hour setting
    final timeFormat = clockStyleProvider.is24HourFormat ? 'HH:mm' : 'h:mm';
    final amPmFormat = clockStyleProvider.is24HourFormat ? '' : ' a';

    return Column(
      children: [
        // Hours and minutes
        Text(
          DateFormat(timeFormat).format(_currentTime),
          style: fontProvider?.getDigitalClockStyle(
            themeProvider: themeProvider,
            fontSize: 80,
          ) ?? TextStyle(
            fontSize: 80,
            fontWeight: FontWeight.w300,
            color: themeProvider.textColor,
          ),
        ),

        // AM/PM indicator if in 12-hour mode
        if (!clockStyleProvider.is24HourFormat)
          Text(
            DateFormat(amPmFormat).format(_currentTime).toUpperCase(),
            style: fontProvider?.getDigitalClockStyle(
              themeProvider: themeProvider,
              fontSize: 20,
              fontWeight: FontWeight.w500,
              color: themeProvider.textColor.withAlpha(180),
            ) ?? TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.w500,
              color: themeProvider.textColor.withAlpha(180),
            ),
          ),

        // Seconds if enabled
        if (showSeconds)
          Text(
            DateFormat('ss').format(_currentTime),
            style: fontProvider?.getDigitalClockStyle(
              themeProvider: themeProvider,
              fontSize: 40,
              fontWeight: FontWeight.w300,
              color: themeProvider.textColor.withAlpha(180),
            ) ?? TextStyle(
              fontSize: 40,
              fontWeight: FontWeight.w300,
              color: themeProvider.textColor.withAlpha(180),
            ),
          ),
      ],
    );
  }

  Widget _buildAnalogClock({
    required AnalogClockStyle style,
    required ThemeProvider themeProvider,
    required ClockStyleProvider clockStyleProvider,
    FontProvider? fontProvider,
  }) {
    final size = MediaQuery.of(context).size.shortestSide * 0.8;

    return AnalogClock(
      time: _currentTime,
      size: size,
      style: style,
      showSecondHand: clockStyleProvider.showSeconds,
      hourHandColor: themeProvider.textColor,
      minuteHandColor: themeProvider.textColor,
      secondHandColor: themeProvider.primaryColor,
      tickColor: themeProvider.textColor.withAlpha(179),
      backgroundColor: themeProvider.backgroundColor.withAlpha(77),
      borderColor: themeProvider.textColor.withAlpha(128),
      fontFamily: fontProvider?.currentFont,
    );
  }

  Widget _buildBinaryClock({
    required ThemeProvider themeProvider,
    required ClockStyleProvider clockStyleProvider,
    FontProvider? fontProvider,
  }) {
    final size = MediaQuery.of(context).size.shortestSide * 0.8;

    return BinaryClock(
      time: _currentTime,
      size: size,
      activeColor: themeProvider.primaryColor,
      inactiveColor: themeProvider.textColor.withAlpha(77),
      backgroundColor: themeProvider.backgroundColor.withAlpha(77),
      showLabels: true,
      fontFamily: fontProvider?.currentFont,
    );
  }

  Widget _buildBatteryInfo(
    BatteryProvider batteryProvider,
    ThemeProvider themeProvider,
    [FontProvider? fontProvider]
  ) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 16),
      decoration: BoxDecoration(
        color: themeProvider.textColor.withAlpha(20),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: themeProvider.textColor.withAlpha(40),
          width: 1,
        ),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            batteryProvider.getBatteryIcon(),
            color: batteryProvider.getBatteryColor(),
            size: 28,
          ),
          const SizedBox(width: 12),
          Text(
            batteryProvider.getBatteryText(),
            style: fontProvider?.getBatteryStyle(
              color: batteryProvider.getBatteryColor(),
              fontSize: 18,
            ) ?? TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.w500,
              color: batteryProvider.getBatteryColor(),
            ),
          ),
        ],
      ),
    );
  }
}
