import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../../core/constants/app_text_styles.dart';
import '../../../providers/theme_provider.dart';
import '../../clock/widgets/analog_clock_painter.dart';
import '../../clock/widgets/digital_clock.dart';

class LivePreview extends StatelessWidget {
  const LivePreview({super.key});

  @override
  Widget build(BuildContext context) {
    final themeProvider = Provider.of<ThemeProvider>(context);
    final now = DateTime.now();

    return Container(
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: themeProvider.surfaceColor,
        borderRadius: BorderRadius.circular(16),
      ),
      child: Column(
        children: [
          // Analog Clock Preview
          SizedBox(
            width: 150,
            height: 150,
            child: CustomPaint(
              painter: AnalogClockPainter(
                time: now,
                backgroundColor: themeProvider.clockBackgroundColor,
                handsColor: themeProvider.clockHandsColor,
                numbersColor: themeProvider.clockNumbersColor,
                ticksColor: themeProvider.clockTicksColor,
              ),
            ),
          ),
          const SizedBox(height: 16),

          // Digital Clock Preview
          DigitalClock(
            time: now,
            style: AppTextStyles.clockTime.copyWith(
              color: themeProvider.clockHandsColor,
            ),
          ),
        ],
      ),
    );
  }
} 