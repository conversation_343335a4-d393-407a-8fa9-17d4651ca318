import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import '../utils/responsive_size.dart';

class FlipBoardClock extends StatelessWidget {
  final DateTime time;
  final bool showSeconds;
  final bool is24HourFormat;

  const FlipBoardClock({
    Key? key,
    required this.time,
    this.showSeconds = true,
    this.is24HourFormat = false,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    // Get period (AM/PM) based on the time passed to the widget
    final isPM = time.hour >= 12;
    final period = isPM ? 'PM' : 'AM';

    // Format date and day of week
    final dateStr = DateFormat('MM/dd/yy').format(time);
    final dayStr = DateFormat('EEEE').format(time);

    // Get time digits
    int hour = time.hour;
    if (!is24HourFormat) {
      hour = hour % 12;
      if (hour == 0) hour = 12;
    }
    final hourStr = hour.toString().padLeft(2, '0');
    final minuteStr = time.minute.toString().padLeft(2, '0');
    final secondsStr = time.second.toString().padLeft(2, '0');

    // Use responsive sizing utility to calculate sizes
    final panelSize = ResponsiveSize.getSize(context, 150); // Base size of 150

    return Container(
      decoration: BoxDecoration(
        color: const Color(0xFF121212),
        borderRadius: BorderRadius.circular(16),
        boxShadow: const [
          BoxShadow(
            color: Color(0x4D000000), // 0.3 opacity black
            blurRadius: 8,
            offset: Offset(0, 4),
          ),
        ],
      ),
      padding: const EdgeInsets.all(16),
      child: SafeArea(
        child: LayoutBuilder(
          builder: (context, constraints) {
            // Use LayoutBuilder to get exact available space
            return SingleChildScrollView(
              child: ConstrainedBox(
                constraints: BoxConstraints(
                  minHeight: constraints.maxHeight,
                ),
                child: IntrinsicHeight(
                  child: Column(
                    children: [
                      const SizedBox(height: 20),

                      // Date and day at the top - make it scrollable if needed
                      SingleChildScrollView(
                        scrollDirection: Axis.horizontal,
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                          children: [
                            // Date (MM/DD/YY)
                            Container(
                              padding: EdgeInsets.symmetric(
                                horizontal: ResponsiveSize.getWidth(context, 0.06),
                                vertical: ResponsiveSize.getHeight(context, 0.015)
                              ),
                              decoration: BoxDecoration(
                                color: const Color(0xFF1C1B1F),
                                borderRadius: BorderRadius.circular(30),
                                boxShadow: const [
                                  BoxShadow(
                                    color: Color(0x33000000), // 0.2 opacity black
                                    blurRadius: 4,
                                    offset: Offset(0, 2),
                                  ),
                                ],
                              ),
                              child: Text(
                                dateStr,
                                style: TextStyle(
                                  color: const Color(0xFFE6E1E5),
                                  fontSize: ResponsiveSize.getFontSize(context, 24),
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                            ),

                            const SizedBox(width: 16),

                            // Day of week
                            Container(
                              padding: EdgeInsets.symmetric(
                                horizontal: ResponsiveSize.getWidth(context, 0.06),
                                vertical: ResponsiveSize.getHeight(context, 0.015)
                              ),
                              decoration: BoxDecoration(
                                color: const Color(0xFF1C1B1F),
                                borderRadius: BorderRadius.circular(30),
                                boxShadow: const [
                                  BoxShadow(
                                    color: Color(0x33000000), // 0.2 opacity black
                                    blurRadius: 4,
                                    offset: Offset(0, 2),
                                  ),
                                ],
                              ),
                              child: Text(
                                dayStr,
                                style: TextStyle(
                                  color: const Color(0xFFE6E1E5),
                                  fontSize: ResponsiveSize.getFontSize(context, 24),
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),

                      const Spacer(),

                      // Main clock display - make it fit within available space
                      FittedBox(
                        fit: BoxFit.scaleDown,
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            // Hours panel
                            _buildFlipPanel(hourStr, panelSize),

                            // Minutes panel
                            _buildFlipPanel(minuteStr, panelSize),
                          ],
                        ),
                      ),

                      const SizedBox(height: 10),

                      // Bottom row with AM/PM and seconds
                      Padding(
                        padding: EdgeInsets.symmetric(
                          horizontal: ResponsiveSize.getWidth(context, 0.04)
                        ),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            // AM/PM indicator
                            if (!is24HourFormat)
                              Text(
                                period,
                                style: TextStyle(
                                  color: const Color(0xFFE6E1E5),
                                  fontSize: ResponsiveSize.getFontSize(context, 32),
                                  fontWeight: FontWeight.bold,
                                  shadows: const [
                                    Shadow(
                                      color: Color(0x4D000000), // 0.3 opacity black
                                      blurRadius: 2,
                                      offset: Offset(0, 1),
                                    ),
                                  ],
                                ),
                              )
                            else
                              const SizedBox.shrink(),

                            const Spacer(),

                            // Seconds
                            if (showSeconds)
                              Text(
                                secondsStr,
                                style: TextStyle(
                                  color: const Color(0xFFE6E1E5),
                                  fontSize: ResponsiveSize.getFontSize(context, 32),
                                  fontWeight: FontWeight.bold,
                                  shadows: const [
                                    Shadow(
                                      color: Color(0x4D000000), // 0.3 opacity black
                                      blurRadius: 2,
                                      offset: Offset(0, 1),
                                    ),
                                  ],
                                ),
                              )
                            else
                              const SizedBox.shrink(),
                          ],
                        ),
                      ),

                      const Spacer(),
                    ],
                  ),
                ),
              ),
            );
          },
        ),
      ),
    );
  }

  // Custom flip panel that looks like the reference image
  Widget _buildFlipPanel(String value, double size) {
    // Split the value into two digits
    final firstDigit = value[0];
    final secondDigit = value[1];

    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 8),
      child: Row(
        children: [
          // First digit
          _buildDigit(firstDigit, size),

          // Second digit
          _buildDigit(secondDigit, size),
        ],
      ),
    );
  }

  // Build a single digit panel with modern Material Design styling
  Widget _buildDigit(String digit, double size) {
    // Calculate a safe font size that won't overflow
    final fontSize = size * 0.6; // Reduced from 0.7 to prevent overflow

    // Modern Material Design colors
    const backgroundColor = Color(0xFF1C1B1F);
    const digitColor = Color(0xFFE6E1E5);
    const dividerColor = Color(0xFF49454F);
    const shadowColor = Color(0x4D000000); // 0.3 opacity black

    return Container(
      width: size * 0.5,
      height: size,
      margin: const EdgeInsets.symmetric(horizontal: 2),
      decoration: BoxDecoration(
        color: backgroundColor,
        borderRadius: BorderRadius.circular(16),
        boxShadow: const [
          BoxShadow(
            color: shadowColor,
            blurRadius: 8,
            offset: Offset(0, 2),
          ),
        ],
      ),
      child: Stack(
        children: [
          // Top half
          Positioned(
            top: 0,
            left: 0,
            right: 0,
            height: size / 2,
            child: Container(
              decoration: BoxDecoration(
                color: backgroundColor,
                borderRadius: const BorderRadius.vertical(top: Radius.circular(16)),
                gradient: const LinearGradient(
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                  colors: [
                    Color(0xFF2B2930),
                    Color(0xFF1C1B1F),
                  ],
                ),
              ),
              child: Center(
                child: FittedBox(
                  fit: BoxFit.contain,
                  child: Text(
                    digit,
                    style: TextStyle(
                      fontSize: fontSize,
                      fontWeight: FontWeight.bold,
                      color: digitColor,
                      shadows: const [
                        Shadow(
                          color: shadowColor,
                          blurRadius: 2,
                          offset: Offset(0, 1),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ),
          ),

          // Dividing line
          Positioned(
            top: size / 2 - 0.5,
            left: 8,
            right: 8,
            height: 1,
            child: Container(color: dividerColor),
          ),

          // Bottom half
          Positioned(
            top: size / 2,
            left: 0,
            right: 0,
            bottom: 0,
            child: Container(
              decoration: BoxDecoration(
                color: backgroundColor,
                borderRadius: const BorderRadius.vertical(bottom: Radius.circular(16)),
                gradient: const LinearGradient(
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                  colors: [
                    Color(0xFF1C1B1F),
                    Color(0xFF2B2930),
                  ],
                ),
              ),
              child: Center(
                child: FittedBox(
                  fit: BoxFit.contain,
                  child: Text(
                    digit,
                    style: TextStyle(
                      fontSize: fontSize,
                      fontWeight: FontWeight.bold,
                      color: digitColor,
                      shadows: const [
                        Shadow(
                          color: shadowColor,
                          blurRadius: 2,
                          offset: Offset(0, 1),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
