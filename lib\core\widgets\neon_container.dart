import 'package:flutter/material.dart';
import '../theme/neon_colors.dart';

class NeonContainer extends StatelessWidget {
  final Color borderColor;
  final double glowIntensity;
  final Widget child;
  final EdgeInsetsGeometry? padding;

  const NeonContainer({
    super.key,
    required this.borderColor,
    required this.glowIntensity,
    required this.child,
    this.padding,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: padding,
      decoration: BoxDecoration(
        color: Colors.transparent,
        borderRadius: BorderRadius.circular(12.0),
        border: Border.all(
          color: borderColor,
          width: 2.0,
        ),
        boxShadow: NeonColors.neonGlow(borderColor, intensity: glowIntensity),
      ),
      child: child,
    );
  }
}

class NeonButton extends StatelessWidget {
  final VoidCallback onPressed;
  final Widget child;
  final Color color;
  final double glowIntensity;

  const NeonButton({
    super.key,
    required this.onPressed,
    required this.child,
    this.color = NeonColors.neonBlue,
    this.glowIntensity = 1.0,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onPressed,
      child: NeonContainer(
        borderColor: color,
        glowIntensity: glowIntensity,
        padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
        child: child,
      ),
    );
  }
} 