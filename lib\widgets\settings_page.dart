import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/theme_provider.dart';
import '../providers/clock_style_provider.dart';

class SettingsPage extends StatelessWidget {
  final bool isLandscape;

  const SettingsPage({
    Key? key,
    required this.isLandscape,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final themeProvider = Provider.of<ThemeProvider>(context);
    final clockStyleProvider = Provider.of<ClockStyleProvider>(context);

    return isLandscape
        ? _buildLandscapeLayout(context, themeProvider, clockStyleProvider)
        : _buildPortraitLayout(context, themeProvider, clockStyleProvider);
  }

  Widget _buildPortraitLayout(
    BuildContext context,
    ThemeProvider themeProvider,
    ClockStyleProvider clockStyleProvider,
  ) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 24.0, vertical: 32.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Page title
          Text(
            'Settings',
            style: TextStyle(
              fontSize: 28,
              fontWeight: FontWeight.bold,
              color: themeProvider.textColor,
            ),
          ),
          const SizedBox(height: 32),

          // Settings content
          Expanded(
            child: _buildSettingsContent(context, themeProvider, clockStyleProvider),
          ),
        ],
      ),
    );
  }

  Widget _buildLandscapeLayout(
    BuildContext context,
    ThemeProvider themeProvider,
    ClockStyleProvider clockStyleProvider,
  ) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 32.0, vertical: 24.0),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Left side - Settings categories
          Expanded(
            flex: 1,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Settings',
                  style: TextStyle(
                    fontSize: 28,
                    fontWeight: FontWeight.bold,
                    color: themeProvider.textColor,
                  ),
                ),
                const SizedBox(height: 32),
                _buildSettingsCategoryButton(
                  context,
                  'Appearance',
                  Icons.palette,
                  true,
                  themeProvider,
                ),
                _buildSettingsCategoryButton(
                  context,
                  'Clock Style',
                  Icons.watch,
                  false,
                  themeProvider,
                ),
                _buildSettingsCategoryButton(
                  context,
                  'Weather',
                  Icons.cloud,
                  false,
                  themeProvider,
                ),
              ],
            ),
          ),

          // Divider
          Container(
            width: 1,
            margin: const EdgeInsets.symmetric(horizontal: 16),
            color: themeProvider.textColor.withAlpha(40),
          ),

          // Right side - Settings content
          Expanded(
            flex: 2,
            child: _buildSettingsContent(context, themeProvider, clockStyleProvider),
          ),
        ],
      ),
    );
  }

  Widget _buildSettingsCategoryButton(
    BuildContext context,
    String title,
    IconData icon,
    bool isSelected,
    ThemeProvider themeProvider,
  ) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 16.0),
      child: InkWell(
        onTap: () {},
        borderRadius: BorderRadius.circular(12),
        child: Container(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
          decoration: BoxDecoration(
            color: isSelected
                ? themeProvider.primaryColor.withAlpha(40)
                : Colors.transparent,
            borderRadius: BorderRadius.circular(12),
          ),
          child: Row(
            children: [
              Icon(
                icon,
                color: isSelected
                    ? themeProvider.primaryColor
                    : themeProvider.textColor,
                size: 24,
              ),
              const SizedBox(width: 16),
              Text(
                title,
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                  color: isSelected
                      ? themeProvider.primaryColor
                      : themeProvider.textColor,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildSettingsContent(
    BuildContext context,
    ThemeProvider themeProvider,
    ClockStyleProvider clockStyleProvider,
  ) {
    return ListView(
      children: [
        // Theme selection
        _buildSettingsSection(
          'Theme',
          [
            _buildThemeSelector(context, themeProvider),
          ],
          themeProvider,
        ),

        // Clock style
        _buildSettingsSection(
          'Clock Style',
          [
            _buildClockStyleSelector(context, clockStyleProvider, themeProvider),
          ],
          themeProvider,
        ),

        // Clock options
        _buildSettingsSection(
          'Clock Options',
          [
            _buildSwitchOption(
              '24-hour format',
              clockStyleProvider.is24HourFormat,
              (value) => clockStyleProvider.set24HourFormat(value),
              themeProvider,
            ),
            _buildSwitchOption(
              'Show seconds',
              clockStyleProvider.showSeconds,
              (value) => clockStyleProvider.setShowSeconds(value),
              themeProvider,
            ),
            _buildSwitchOption(
              'Show date',
              clockStyleProvider.showDate,
              (value) => clockStyleProvider.setShowDate(value),
              themeProvider,
            ),
            _buildSwitchOption(
              'Show day of week',
              clockStyleProvider.showDayOfWeek,
              (value) => clockStyleProvider.setShowDayOfWeek(value),
              themeProvider,
            ),
          ],
          themeProvider,
        ),
      ],
    );
  }

  Widget _buildSettingsSection(
    String title,
    List<Widget> children,
    ThemeProvider themeProvider,
  ) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 24.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            title,
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: themeProvider.textColor,
            ),
          ),
          const SizedBox(height: 16),
          Container(
            decoration: BoxDecoration(
              color: themeProvider.textColor.withAlpha(10),
              borderRadius: BorderRadius.circular(16),
            ),
            child: Column(
              children: children,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildThemeSelector(BuildContext context, ThemeProvider themeProvider) {
    return Column(
      children: themeProvider.availableThemes.map((theme) {
        final isSelected = theme == themeProvider.selectedTheme;
        return InkWell(
          onTap: () => themeProvider.setTheme(theme),
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
            child: Row(
              children: [
                Container(
                  width: 24,
                  height: 24,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    color: isSelected ? themeProvider.primaryColor : Colors.transparent,
                    border: Border.all(
                      color: isSelected
                          ? themeProvider.primaryColor
                          : themeProvider.textColor.withAlpha(100),
                      width: 2,
                    ),
                  ),
                  child: isSelected
                      ? const Icon(
                          Icons.check,
                          color: Colors.white,
                          size: 16,
                        )
                      : null,
                ),
                const SizedBox(width: 16),
                Text(
                  theme,
                  style: TextStyle(
                    fontSize: 16,
                    color: themeProvider.textColor,
                  ),
                ),
              ],
            ),
          ),
        );
      }).toList(),
    );
  }

  Widget _buildClockStyleSelector(
    BuildContext context,
    ClockStyleProvider clockStyleProvider,
    ThemeProvider themeProvider,
  ) {
    // Group clock styles by category
    final Map<String, List<ClockStyle>> groupedStyles = {
      'Digital': [
        ClockStyle.digital,
        ClockStyle.digitalWithSeconds,
      ],
      'Analog': [
        ClockStyle.analogBasic,
        ClockStyle.analogModern,
        ClockStyle.analogMinimal,
      ],
      'Special': [
        ClockStyle.binaryClock,
      ],
    };

    return Column(
      children: groupedStyles.entries.map((entry) {
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Category header
            Padding(
              padding: const EdgeInsets.fromLTRB(16, 12, 16, 8),
              child: Text(
                entry.key,
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.bold,
                  color: themeProvider.textColor.withAlpha(150),
                ),
              ),
            ),

            // Styles in this category
            ...entry.value.map((style) {
              final isSelected = style == clockStyleProvider.currentStyle;
              return InkWell(
                onTap: () => clockStyleProvider.setClockStyle(style),
                child: Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                  child: Row(
                    children: [
                      Container(
                        width: 24,
                        height: 24,
                        decoration: BoxDecoration(
                          shape: BoxShape.circle,
                          color: isSelected ? themeProvider.primaryColor : Colors.transparent,
                          border: Border.all(
                            color: isSelected
                                ? themeProvider.primaryColor
                                : themeProvider.textColor.withAlpha(100),
                            width: 2,
                          ),
                        ),
                        child: isSelected
                            ? const Icon(
                                Icons.check,
                                color: Colors.white,
                                size: 16,
                              )
                            : null,
                      ),
                      const SizedBox(width: 16),
                      Expanded(
                        child: Text(
                          clockStyleProvider.getStyleName(style),
                          style: TextStyle(
                            fontSize: 16,
                            color: themeProvider.textColor,
                          ),
                        ),
                      ),
                      // Removed flip clock theme indicator
                    ],
                  ),
                ),
              );
            }).toList(),

            // Divider between categories
            if (entry.key != groupedStyles.keys.last)
              Divider(height: 1, color: themeProvider.textColor.withAlpha(30)),
          ],
        );
      }).toList(),
    );
  }

  Widget _buildSwitchOption(
    String title,
    bool value,
    Function(bool) onChanged,
    ThemeProvider themeProvider,
  ) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            title,
            style: TextStyle(
              fontSize: 16,
              color: themeProvider.textColor,
            ),
          ),
          Switch(
            value: value,
            onChanged: onChanged,
            activeColor: themeProvider.primaryColor,
          ),
        ],
      ),
    );
  }
}
