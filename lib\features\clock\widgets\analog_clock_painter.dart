import 'dart:math';
import 'package:flutter/material.dart';

class AnalogClockPainter extends CustomPainter {
  final DateTime time;
  final Color backgroundColor;
  final Color handsColor;
  final Color numbersColor;
  final Color ticksColor;

  AnalogClockPainter({
    required this.time,
    required this.backgroundColor,
    required this.handsColor,
    required this.numbersColor,
    required this.ticksColor,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final center = Offset(size.width / 2, size.height / 2);
    final radius = min(size.width, size.height) / 2;

    // Draw background
    final backgroundPaint = Paint()
      ..color = backgroundColor
      ..style = PaintingStyle.fill;
    canvas.drawCircle(center, radius, backgroundPaint);

    // Draw ticks
    final tickPaint = Paint()
      ..color = ticksColor
      ..strokeWidth = 1.0
      ..style = PaintingStyle.stroke;

    for (var i = 0; i < 60; i++) {
      final angle = i * 6 * pi / 180;
      final tickLength = i % 5 == 0 ? 10.0 : 5.0;
      final tickStart = Offset(
        center.dx + (radius - tickLength) * cos(angle),
        center.dy + (radius - tickLength) * sin(angle),
      );
      final tickEnd = Offset(
        center.dx + radius * cos(angle),
        center.dy + radius * sin(angle),
      );
      canvas.drawLine(tickStart, tickEnd, tickPaint);
    }

    // Draw numbers
    final textPainter = TextPainter(
      textDirection: TextDirection.ltr,
      textAlign: TextAlign.center,
    );

    for (var i = 1; i <= 12; i++) {
      final angle = i * 30 * pi / 180;
      final numberRadius = radius - 30;
      final numberOffset = Offset(
        center.dx + numberRadius * cos(angle - pi / 2),
        center.dy + numberRadius * sin(angle - pi / 2),
      );

      textPainter.text = TextSpan(
        text: i.toString(),
        style: TextStyle(
          color: numbersColor,
          fontSize: 16,
          fontWeight: FontWeight.bold,
        ),
      );
      textPainter.layout();
      textPainter.paint(
        canvas,
        Offset(
          numberOffset.dx - textPainter.width / 2,
          numberOffset.dy - textPainter.height / 2,
        ),
      );
    }

    // Draw hands
    final hourAngle = (time.hour % 12 + time.minute / 60) * 30 * pi / 180;
    final minuteAngle = time.minute * 6 * pi / 180;
    final secondAngle = time.second * 6 * pi / 180;

    final handPaint = Paint()
      ..color = handsColor
      ..style = PaintingStyle.stroke
      ..strokeCap = StrokeCap.round;

    // Hour hand
    handPaint.strokeWidth = 4.0;
    canvas.drawLine(
      center,
      Offset(
        center.dx + radius * 0.5 * cos(hourAngle - pi / 2),
        center.dy + radius * 0.5 * sin(hourAngle - pi / 2),
      ),
      handPaint,
    );

    // Minute hand
    handPaint.strokeWidth = 3.0;
    canvas.drawLine(
      center,
      Offset(
        center.dx + radius * 0.7 * cos(minuteAngle - pi / 2),
        center.dy + radius * 0.7 * sin(minuteAngle - pi / 2),
      ),
      handPaint,
    );

    // Second hand
    handPaint.strokeWidth = 2.0;
    canvas.drawLine(
      center,
      Offset(
        center.dx + radius * 0.8 * cos(secondAngle - pi / 2),
        center.dy + radius * 0.8 * sin(secondAngle - pi / 2),
      ),
      handPaint,
    );

    // Center dot
    final centerPaint = Paint()
      ..color = handsColor
      ..style = PaintingStyle.fill;
    canvas.drawCircle(center, 4.0, centerPaint);
  }

  @override
  bool shouldRepaint(AnalogClockPainter oldDelegate) {
    return oldDelegate.time != time ||
        oldDelegate.backgroundColor != backgroundColor ||
        oldDelegate.handsColor != handsColor ||
        oldDelegate.numbersColor != numbersColor ||
        oldDelegate.ticksColor != ticksColor;
  }
} 