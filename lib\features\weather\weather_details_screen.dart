import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../core/theme/neon_colors.dart';
import '../../providers/color_provider.dart';
import '../../providers/theme_provider.dart';
import '../../providers/weather_provider.dart';
import '../../core/widgets/neon_container.dart';

class WeatherDetailsScreen extends StatefulWidget {
  final String location;

  const WeatherDetailsScreen({Key? key, required this.location}) : super(key: key);

  @override
  State<WeatherDetailsScreen> createState() => _WeatherDetailsScreenState();
}

class _WeatherDetailsScreenState extends State<WeatherDetailsScreen> with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _fadeAnimation;
  late Animation<double> _scaleAnimation;
  late Animation<Offset> _slideAnimation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _controller, curve: Curves.easeIn),
    );

    _scaleAnimation = Tween<double>(begin: 0.9, end: 1.0).animate(
      CurvedAnimation(parent: _controller, curve: Curves.easeOut),
    );

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.1),
      end: Offset.zero,
    ).animate(CurvedAnimation(parent: _controller, curve: Curves.easeOut));

    _controller.forward();
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final colorProvider = Provider.of<ColorProvider>(context);
    final themeProvider = Provider.of<ThemeProvider>(context);
    final weatherProvider = Provider.of<WeatherProvider>(context);

    return Container(
      decoration: const BoxDecoration(
        gradient: NeonColors.cyberGradient,
      ),
      child: Scaffold(
        backgroundColor: Colors.transparent,
        extendBodyBehindAppBar: true,
        appBar: AppBar(
          backgroundColor: Colors.transparent,
          elevation: 0,
          leading: IconButton(
            icon: Icon(
              Icons.arrow_back,
              color: colorProvider.currentColor,
              shadows: NeonColors.textGlow(
                colorProvider.currentColor,
                intensity: themeProvider.glowIntensity,
              ),
            ),
            onPressed: () => Navigator.pop(context),
          ),
          title: Row(
            children: [
              Icon(
                Icons.cloud,
                color: colorProvider.currentColor,
                shadows: NeonColors.textGlow(
                  colorProvider.currentColor,
                  intensity: themeProvider.glowIntensity,
                ),
              ),
              const SizedBox(width: 8),
              Text(
                'Weather Details',
                style: TextStyle(
                  color: colorProvider.currentColor,
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                  shadows: NeonColors.textGlow(
                    colorProvider.currentColor,
                    intensity: themeProvider.glowIntensity,
                  ),
                ),
              ),
            ],
          ),
          centerTitle: false,
        ),
        body: SafeArea(
          child: FadeTransition(
            opacity: _fadeAnimation,
            child: ScaleTransition(
              scale: _scaleAnimation,
              child: SlideTransition(
                position: _slideAnimation,
                child: _buildWeatherContent(weatherProvider, colorProvider, themeProvider),
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildWeatherContent(
    WeatherProvider weatherProvider,
    ColorProvider colorProvider,
    ThemeProvider themeProvider,
  ) {
    if (weatherProvider.isLoading) {
      return Center(
        child: CircularProgressIndicator(
          color: colorProvider.currentColor,
        ),
      );
    }

    if (weatherProvider.error != null) {
      return Center(
        child: Text(
          weatherProvider.error!,
          style: TextStyle(
            color: colorProvider.currentColor,
            fontSize: 18,
            shadows: NeonColors.textGlow(
              colorProvider.currentColor,
              intensity: themeProvider.glowIntensity,
            ),
          ),
        ),
      );
    }

    if (weatherProvider.weather == null) {
      return Center(
        child: Text(
          'No weather data available',
          style: TextStyle(
            color: colorProvider.currentColor,
            fontSize: 18,
            shadows: NeonColors.textGlow(
              colorProvider.currentColor,
              intensity: themeProvider.glowIntensity,
            ),
          ),
        ),
      );
    }

    final weather = weatherProvider.weather!;

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          // Location and current conditions
          NeonContainer(
            borderColor: colorProvider.currentColor,
            glowIntensity: themeProvider.glowIntensity,
            padding: const EdgeInsets.all(20),
            child: Column(
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      Icons.location_on,
                      color: colorProvider.currentColor,
                      size: 28,
                      shadows: NeonColors.textGlow(
                        colorProvider.currentColor,
                        intensity: themeProvider.glowIntensity,
                      ),
                    ),
                    const SizedBox(width: 8),
                    Text(
                      weather.location,
                      style: TextStyle(
                        color: colorProvider.currentColor,
                        fontSize: 24,
                        fontWeight: FontWeight.bold,
                        shadows: NeonColors.textGlow(
                          colorProvider.currentColor,
                          intensity: themeProvider.glowIntensity,
                        ),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 20),
                Text(
                  weather.temperature,
                  style: TextStyle(
                    color: colorProvider.currentColor,
                    fontSize: 64,
                    fontWeight: FontWeight.bold,
                    shadows: NeonColors.textGlow(
                      colorProvider.currentColor,
                      intensity: themeProvider.glowIntensity,
                    ),
                  ),
                ),
                Text(
                  weather.condition,
                  style: TextStyle(
                    color: colorProvider.currentColor,
                    fontSize: 28,
                    shadows: NeonColors.textGlow(
                      colorProvider.currentColor,
                      intensity: themeProvider.glowIntensity,
                    ),
                  ),
                ),
              ],
            ),
          ),

          const SizedBox(height: 20),

          // Weather details
          NeonContainer(
            borderColor: colorProvider.currentColor,
            glowIntensity: themeProvider.glowIntensity,
            padding: const EdgeInsets.all(20),
            child: Column(
              children: [
                _buildDetailItem(
                  icon: Icons.water_drop,
                  label: 'Humidity',
                  value: weather.humidity,
                  colorProvider: colorProvider,
                  themeProvider: themeProvider,
                ),
                const Divider(height: 30, color: Colors.white24),
                _buildDetailItem(
                  icon: Icons.air,
                  label: 'Wind Speed',
                  value: weather.windSpeed,
                  colorProvider: colorProvider,
                  themeProvider: themeProvider,
                ),
                const Divider(height: 30, color: Colors.white24),
                _buildDetailItem(
                  icon: Icons.thermostat,
                  label: 'Feels Like',
                  value: weather.feelsLike,
                  colorProvider: colorProvider,
                  themeProvider: themeProvider,
                ),
              ],
            ),
          ),

          const SizedBox(height: 20),

          // Forecast section (placeholder for future implementation)
          NeonContainer(
            borderColor: colorProvider.currentColor,
            glowIntensity: themeProvider.glowIntensity,
            padding: const EdgeInsets.all(20),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Forecast',
                  style: TextStyle(
                    color: colorProvider.currentColor,
                    fontSize: 22,
                    fontWeight: FontWeight.bold,
                    shadows: NeonColors.textGlow(
                      colorProvider.currentColor,
                      intensity: themeProvider.glowIntensity,
                    ),
                  ),
                ),
                const SizedBox(height: 16),
                Text(
                  'Forecast data will be available in future updates.',
                  style: TextStyle(
                    color: colorProvider.currentColor,
                    fontSize: 16,
                    shadows: NeonColors.textGlow(
                      colorProvider.currentColor,
                      intensity: themeProvider.glowIntensity * 0.5,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDetailItem({
    required IconData icon,
    required String label,
    required String value,
    required ColorProvider colorProvider,
    required ThemeProvider themeProvider,
  }) {
    return Row(
      children: [
        Icon(
          icon,
          color: colorProvider.currentColor,
          size: 28,
          shadows: NeonColors.textGlow(
            colorProvider.currentColor,
            intensity: themeProvider.glowIntensity,
          ),
        ),
        const SizedBox(width: 16),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                label,
                style: TextStyle(
                  color: colorProvider.currentColor.withAlpha(204), // 0.8 * 255 = 204
                  fontSize: 16,
                  shadows: NeonColors.textGlow(
                    colorProvider.currentColor,
                    intensity: themeProvider.glowIntensity * 0.5,
                  ),
                ),
              ),
              Text(
                value,
                style: TextStyle(
                  color: colorProvider.currentColor,
                  fontSize: 22,
                  fontWeight: FontWeight.bold,
                  shadows: NeonColors.textGlow(
                    colorProvider.currentColor,
                    intensity: themeProvider.glowIntensity,
                  ),
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }
}
