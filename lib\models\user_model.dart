class UserModel {
  final String id;
  final String email;
  final String displayName;
  final String? photoUrl;
  final List<String> purchasedThemes;
  final List<String> createdThemes;

  UserModel({
    required this.id,
    required this.email,
    required this.displayName,
    this.photoUrl,
    this.purchasedThemes = const [],
    this.createdThemes = const [],
  });

  // Convert to Map for Firestore
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'email': email,
      'displayName': displayName,
      'photoUrl': photoUrl,
      'purchasedThemes': purchasedThemes,
      'createdThemes': createdThemes,
    };
  }

  // Create from Firestore Map
  factory UserModel.fromMap(Map<String, dynamic> map) {
    return UserModel(
      id: map['id'] ?? '',
      email: map['email'] ?? '',
      displayName: map['displayName'] ?? '',
      photoUrl: map['photoUrl'],
      purchasedThemes: List<String>.from(map['purchasedThemes'] ?? []),
      createdThemes: List<String>.from(map['createdThemes'] ?? []),
    );
  }

  // Create a copy with updated fields
  UserModel copyWith({
    String? id,
    String? email,
    String? displayName,
    String? photoUrl,
    List<String>? purchasedThemes,
    List<String>? createdThemes,
  }) {
    return UserModel(
      id: id ?? this.id,
      email: email ?? this.email,
      displayName: displayName ?? this.displayName,
      photoUrl: photoUrl ?? this.photoUrl,
      purchasedThemes: purchasedThemes ?? this.purchasedThemes,
      createdThemes: createdThemes ?? this.createdThemes,
    );
  }
} 