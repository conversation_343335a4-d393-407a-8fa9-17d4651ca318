import 'dart:async';
import 'package:flutter/material.dart';

class SingleFlipClock extends StatefulWidget {
  final bool showSeconds;
  final bool is24HourFormat;
  final Color digitColor;
  final Color backgroundColor;

  const SingleFlipClock({
    Key? key,
    this.showSeconds = true,
    this.is24HourFormat = false,
    this.digitColor = Colors.white,
    this.backgroundColor = const Color(0xFF1C1B1F),
  }) : super(key: key);

  @override
  State<SingleFlipClock> createState() => _SingleFlipClockState();
}

class _SingleFlipClockState extends State<SingleFlipClock> {
  late Timer _timer;
  late DateTime _currentTime;

  @override
  void initState() {
    super.initState();
    _currentTime = DateTime.now();
    _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      setState(() {
        _currentTime = DateTime.now();
      });
    });
  }

  @override
  void dispose() {
    _timer.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    // Format time based on settings
    int hour = _currentTime.hour;
    String period = '';

    if (!widget.is24HourFormat) {
      period = hour >= 12 ? 'PM' : 'AM';
      hour = hour % 12;
      if (hour == 0) hour = 12;
    }

    final hourStr = hour.toString().padLeft(2, '0');
    final minuteStr = _currentTime.minute.toString().padLeft(2, '0');
    final secondStr = _currentTime.second.toString().padLeft(2, '0');

    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        // Hours
        _buildDigitGroup(hourStr),

        // Separator
        _buildSeparator(),

        // Minutes
        _buildDigitGroup(minuteStr),

        // Seconds (optional)
        if (widget.showSeconds) ...[
          _buildSeparator(),
          _buildDigitGroup(secondStr),
        ],

        // AM/PM indicator (for 12-hour format)
        if (!widget.is24HourFormat && period.isNotEmpty) ...[
          const SizedBox(width: 16),
          _buildPeriodIndicator(period),
        ],
      ],
    );
  }

  Widget _buildDigitGroup(String value) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        _buildSingleDigit(value[0]),
        const SizedBox(width: 4),
        _buildSingleDigit(value[1]),
      ],
    );
  }

  Widget _buildSingleDigit(String digit) {
    return Container(
      width: 50,
      height: 80,
      decoration: BoxDecoration(
        color: widget.backgroundColor,
        borderRadius: BorderRadius.circular(8),
        boxShadow: const [
          BoxShadow(
            color: Color(0x40000000),
            blurRadius: 4,
            offset: Offset(0, 2),
          ),
        ],
      ),
      child: Stack(
        children: [
          // Main digit
          Center(
            child: Text(
              digit,
              style: TextStyle(
                fontSize: 50,
                fontWeight: FontWeight.bold,
                color: widget.digitColor,
              ),
            ),
          ),

          // Top half overlay with slight gradient
          Positioned(
            top: 0,
            left: 0,
            right: 0,
            height: 40,
            child: Container(
              decoration: BoxDecoration(
                borderRadius: const BorderRadius.vertical(
                  top: Radius.circular(8),
                ),
                // Use a simple color instead of gradient to avoid deprecation warnings
                color: widget.backgroundColor,
              ),
            ),
          ),

          // Bottom half overlay with slight gradient
          Positioned(
            bottom: 0,
            left: 0,
            right: 0,
            height: 40,
            child: Container(
              decoration: BoxDecoration(
                borderRadius: const BorderRadius.vertical(
                  bottom: Radius.circular(8),
                ),
                // Use a simple color instead of gradient to avoid deprecation warnings
                color: widget.backgroundColor,
              ),
            ),
          ),

          // Center divider line
          Positioned(
            top: 39.5,
            left: 0,
            right: 0,
            height: 1,
            child: Container(
              color: const Color(0x4D000000), // 0.3 opacity black
            ),
          ),

          // Digit on top half (clipped)
          Positioned(
            top: 0,
            left: 0,
            right: 0,
            height: 40,
            child: ClipRect(
              child: Align(
                alignment: Alignment.center,
                child: Text(
                  digit,
                  style: TextStyle(
                    fontSize: 50,
                    fontWeight: FontWeight.bold,
                    color: widget.digitColor,
                  ),
                ),
              ),
            ),
          ),

          // Digit on bottom half (clipped)
          Positioned(
            top: 40,
            left: 0,
            right: 0,
            height: 40,
            child: ClipRect(
              child: Align(
                alignment: Alignment.center,
                child: Text(
                  digit,
                  style: TextStyle(
                    fontSize: 50,
                    fontWeight: FontWeight.bold,
                    color: widget.digitColor,
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSeparator() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 8),
      child: Text(
        ':',
        style: TextStyle(
          fontSize: 40,
          fontWeight: FontWeight.bold,
          color: widget.digitColor,
        ),
      ),
    );
  }

  Widget _buildPeriodIndicator(String period) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: widget.backgroundColor,
        borderRadius: BorderRadius.circular(8),
        boxShadow: const [
          BoxShadow(
            color: Color(0x40000000),
            blurRadius: 4,
            offset: Offset(0, 2),
          ),
        ],
      ),
      child: Text(
        period,
        style: TextStyle(
          fontSize: 24,
          fontWeight: FontWeight.bold,
          color: widget.digitColor,
        ),
      ),
    );
  }
}
