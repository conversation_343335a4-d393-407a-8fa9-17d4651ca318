import 'dart:math';
import 'package:flutter/material.dart';

class Analog<PERSON>lock extends StatelessWidget {
  final DateTime time;
  final Color hourHandColor;
  final Color minuteHandColor;
  final Color secondHandColor;
  final Color tickColor;
  final Color backgroundColor;
  final Color borderColor;
  final bool showSecondHand;
  final bool showNumbers;
  final bool showTicks;
  final bool showBorder;
  final double size;
  final AnalogClockStyle style;
  final String? fontFamily;

  const AnalogClock({
    Key? key,
    required this.time,
    this.hourHandColor = Colors.white,
    this.minuteHandColor = Colors.white,
    this.secondHandColor = Colors.red,
    this.tickColor = Colors.white,
    this.backgroundColor = Colors.black,
    this.borderColor = Colors.white,
    this.showSecondHand = true,
    this.showNumbers = true,
    this.showTicks = true,
    this.showBorder = true,
    this.size = 300,
    this.style = AnalogClockStyle.basic,
    this.fontFamily,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      width: size,
      height: size,
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        color: backgroundColor,
        border: showBorder ? Border.all(color: borderColor, width: 4) : null,
        boxShadow: style == AnalogClockStyle.modern
            ? [
                BoxShadow(
                  color: borderColor.withAlpha(77),
                  blurRadius: 15,
                  spreadRadius: 5,
                ),
              ]
            : null,
      ),
      child: Stack(
        children: [
          // Clock face
          if (showTicks) _buildClockFace(),

          // Hour hand
          _buildHourHand(),

          // Minute hand
          _buildMinuteHand(),

          // Second hand
          if (showSecondHand) _buildSecondHand(),

          // Center dot
          Center(
            child: Container(
              width: size * 0.05,
              height: size * 0.05,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                color: style == AnalogClockStyle.minimal ? secondHandColor : borderColor,
                border: Border.all(
                  color: style == AnalogClockStyle.minimal ? Colors.transparent : secondHandColor,
                  width: 2,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildClockFace() {
    return CustomPaint(
      size: Size(size, size),
      painter: ClockFacePainter(
        tickColor: tickColor,
        showNumbers: showNumbers,
        style: style,
        fontFamily: fontFamily,
      ),
    );
  }

  Widget _buildHourHand() {
    final hour = time.hour % 12;
    final minute = time.minute;
    final angle = (hour + (minute / 60)) * (2 * pi / 12) - pi / 2;

    return _buildClockHand(
      angle: angle,
      handLength: size * 0.3,
      handWidth: size * 0.04,
      color: hourHandColor,
      style: style,
    );
  }

  Widget _buildMinuteHand() {
    final minute = time.minute;
    final second = time.second;
    final angle = (minute + (second / 60)) * (2 * pi / 60) - pi / 2;

    return _buildClockHand(
      angle: angle,
      handLength: size * 0.4,
      handWidth: size * 0.03,
      color: minuteHandColor,
      style: style,
    );
  }

  Widget _buildSecondHand() {
    final second = time.second;
    final millisecond = time.millisecond;
    final angle = (second + (millisecond / 1000)) * (2 * pi / 60) - pi / 2;

    return _buildClockHand(
      angle: angle,
      handLength: size * 0.45,
      handWidth: size * 0.01,
      color: secondHandColor,
      style: style,
      isSecondHand: true,
    );
  }

  Widget _buildClockHand({
    required double angle,
    required double handLength,
    required double handWidth,
    required Color color,
    required AnalogClockStyle style,
    bool isSecondHand = false,
  }) {
    return Center(
      child: Transform.rotate(
        angle: angle,
        child: style == AnalogClockStyle.minimal
            ? _buildMinimalHand(handLength, handWidth, color, isSecondHand)
            : _buildStandardHand(handLength, handWidth, color, isSecondHand),
      ),
    );
  }

  Widget _buildStandardHand(
    double handLength,
    double handWidth,
    Color color,
    bool isSecondHand,
  ) {
    return Container(
      height: handLength,
      width: handWidth,
      decoration: BoxDecoration(
        color: color,
        borderRadius: BorderRadius.circular(handWidth / 2),
      ),
      alignment: Alignment.topCenter,
      transformAlignment: Alignment.bottomCenter,
    );
  }

  Widget _buildMinimalHand(
    double handLength,
    double handWidth,
    Color color,
    bool isSecondHand,
  ) {
    return Container(
      height: handLength,
      width: 1,
      color: color,
      alignment: Alignment.topCenter,
      transformAlignment: Alignment.bottomCenter,
    );
  }
}

class ClockFacePainter extends CustomPainter {
  final Color tickColor;
  final bool showNumbers;
  final AnalogClockStyle style;
  final String? fontFamily;

  ClockFacePainter({
    required this.tickColor,
    required this.showNumbers,
    required this.style,
    this.fontFamily,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final center = Offset(size.width / 2, size.height / 2);
    final radius = size.width / 2;

    final paint = Paint()
      ..color = tickColor
      ..strokeWidth = 2
      ..style = PaintingStyle.stroke;

    // Draw hour ticks
    for (int i = 0; i < 12; i++) {
      final angle = i * (2 * pi / 12);
      final outerPoint = Offset(
        center.dx + radius * 0.9 * cos(angle),
        center.dy + radius * 0.9 * sin(angle),
      );
      final innerPoint = Offset(
        center.dx + radius * 0.8 * cos(angle),
        center.dy + radius * 0.8 * sin(angle),
      );

      canvas.drawLine(innerPoint, outerPoint, paint);

      if (showNumbers && style != AnalogClockStyle.minimal) {
        final textPainter = TextPainter(
          text: TextSpan(
            text: i == 0 ? '12' : i.toString(),
            style: TextStyle(
              color: tickColor,
              fontSize: radius * 0.15,
              fontWeight: FontWeight.bold,
              fontFamily: fontFamily,
            ),
          ),
          textDirection: TextDirection.ltr,
        );

        textPainter.layout();

        final numberPoint = Offset(
          center.dx + radius * 0.7 * cos(angle) - textPainter.width / 2,
          center.dy + radius * 0.7 * sin(angle) - textPainter.height / 2,
        );

        textPainter.paint(canvas, numberPoint);
      }
    }

    // Draw minute ticks
    if (style != AnalogClockStyle.minimal) {
      paint.strokeWidth = 1;
      for (int i = 0; i < 60; i++) {
        if (i % 5 != 0) {  // Skip hour ticks
          final angle = i * (2 * pi / 60);
          final outerPoint = Offset(
            center.dx + radius * 0.9 * cos(angle),
            center.dy + radius * 0.9 * sin(angle),
          );
          final innerPoint = Offset(
            center.dx + radius * 0.85 * cos(angle),
            center.dy + radius * 0.85 * sin(angle),
          );

          canvas.drawLine(innerPoint, outerPoint, paint);
        }
      }
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
}

enum AnalogClockStyle {
  basic,
  modern,
  minimal,
}
