import 'package:flutter/material.dart';

class NeonColors {
  // Modern neon colors with better visual appeal
  static const neonBlue = Color(0xFF00E5FF);
  static const neonPink = Color(0xFFFF00E5);
  static const neonPurple = Color(0xFFAA00FF);
  static const neonGreen = Color(0xFF00E676);
  static const neonYellow = Color(0xFFFFEA00);
  static const neonRed = Color(0xFFFF1744);
  static const neonCyan = Color(0xFF18FFFF);
  static const neonOrange = Color(0xFFFF9100);
  static const neonTeal = Color(0xFF1DE9B6);
  static const neonLime = Color(0xFFC6FF00);

  // Trending modern colors (2023-2024)
  static const vaporwavePink = Color(0xFFFF6AD5);
  static const vaporwaveBlue = Color(0xFF26D0CE);
  static const retroWaveRed = Color(0xFFFF2E63);
  static const retroWaveBlue = Color(0xFF252A34);
  static const retroWavePurple = Color(0xFF8236CB);
  static const mintGreen = Color(0xFF98DDCA);
  static const pastelLavender = Color(0xFFD4BBFC);
  static const pastelPink = Color(0xFFFFB6C1);
  static const pastelBlue = Color(0xFFADD8E6);
  static const pastelYellow = Color(0xFFFFFACD);
  static const modernGold = Color(0xFFFFD700);
  static const modernSilver = Color(0xFFC0C0C0);
  static const modernRose = Color(0xFFFF7F7F);
  static const modernSky = Color(0xFF87CEEB);
  static const modernMint = Color(0xFF98FB98);

  // Background colors
  static const darkCyber = Color(0xFF0A0A0A);
  static const cyberGradient = LinearGradient(
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
    colors: [
      Color(0xFF121212),
      Color(0xFF0A0A14),
    ],
  );

  // Material Design inspired gradients
  static const materialGradient = LinearGradient(
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
    colors: [
      Color(0xFF6200EA),
      Color(0xFF3700B3),
    ],
  );

  // Trending theme gradients
  static const vaporwaveGradient = LinearGradient(
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
    colors: [
      Color(0xFF0093E9),
      Color(0xFF80D0C7),
    ],
  );

  static const retroWaveGradient = LinearGradient(
    begin: Alignment.topCenter,
    end: Alignment.bottomCenter,
    colors: [
      Color(0xFF0F0F1B),
      Color(0xFF2E1A47),
      Color(0xFF541388),
    ],
  );

  static const pastelGradient = LinearGradient(
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
    colors: [
      Color(0xFFA6C0FE),
      Color(0xFFF68084),
    ],
  );

  static const modernMinimalGradient = LinearGradient(
    begin: Alignment.topCenter,
    end: Alignment.bottomCenter,
    colors: [
      Color(0xFFFFFFFF),
      Color(0xFFF5F5F5),
    ],
  );

  static const darkModeGradient = LinearGradient(
    begin: Alignment.topCenter,
    end: Alignment.bottomCenter,
    colors: [
      Color(0xFF121212),
      Color(0xFF1E1E1E),
    ],
  );

  static const sunriseGradient = LinearGradient(
    begin: Alignment.topCenter,
    end: Alignment.bottomCenter,
    colors: [
      Color(0xFFFF512F),
      Color(0xFFF09819),
    ],
  );

  static const nightSkyGradient = LinearGradient(
    begin: Alignment.topCenter,
    end: Alignment.bottomCenter,
    colors: [
      Color(0xFF0F2027),
      Color(0xFF203A43),
      Color(0xFF2C5364),
    ],
  );

  // Rainbow colors - more visually pleasing sequence
  static const List<Color> rainbowColors = [
    neonRed,
    neonOrange,
    neonYellow,
    neonGreen,
    neonTeal,
    neonCyan,
    neonBlue,
    neonPurple,
    neonPink,
  ];

  // Trending color palettes
  static const List<Color> vaporwavePalette = [
    vaporwavePink,
    vaporwaveBlue,
    neonPurple,
    neonCyan,
    Color(0xFF91EAE4), // Light cyan
  ];

  static const List<Color> retroWavePalette = [
    retroWaveRed,
    retroWaveBlue,
    retroWavePurple,
    neonCyan,
    neonPink,
  ];

  static const List<Color> pastelPalette = [
    pastelPink,
    pastelBlue,
    pastelLavender,
    pastelYellow,
    mintGreen,
  ];

  static const List<Color> modernMinimalPalette = [
    Color(0xFF000000), // Black
    Color(0xFF333333), // Dark gray
    Color(0xFF666666), // Medium gray
    Color(0xFF999999), // Light gray
    Color(0xFFFFFFFF), // White
  ];

  static const List<Color> sunrisePalette = [
    Color(0xFFFF512F), // Sunset orange
    Color(0xFFF09819), // Golden yellow
    Color(0xFFFF7E5F), // Coral
    Color(0xFFFF9966), // Peach
    Color(0xFFFFCC33), // Amber
  ];

  static const List<Color> nightSkyPalette = [
    Color(0xFF0F2027), // Deep blue-black
    Color(0xFF203A43), // Dark blue
    Color(0xFF2C5364), // Navy blue
    Color(0xFF3A6073), // Steel blue
    Color(0xFF5A8DB8), // Medium blue
  ];

  // Helper methods
  static List<BoxShadow> neonGlow(Color color, {double intensity = 1.0}) {
    // Using fixed colors to avoid deprecation warnings
    final Color color1 = _adjustColorOpacity(color, 0.3 * intensity);
    final Color color2 = _adjustColorOpacity(color, 0.2 * intensity);
    final Color color3 = _adjustColorOpacity(color, 0.1 * intensity);

    return [
      BoxShadow(
        color: color1,
        blurRadius: 8.0,
        spreadRadius: 2.0,
      ),
      BoxShadow(
        color: color2,
        blurRadius: 16.0,
        spreadRadius: 4.0,
      ),
      BoxShadow(
        color: color3,
        blurRadius: 24.0,
        spreadRadius: 6.0,
      ),
    ];
  }

  static List<Shadow> textGlow(Color color, {double intensity = 1.0}) {
    // Using fixed colors to avoid deprecation warnings
    final Color color1 = _adjustColorOpacity(color, 0.3 * intensity);
    final Color color2 = _adjustColorOpacity(color, 0.2 * intensity);
    final Color color3 = _adjustColorOpacity(color, 0.1 * intensity);

    return [
      Shadow(
        color: color1,
        blurRadius: 8.0,
        offset: const Offset(0, 0),
      ),
      Shadow(
        color: color2,
        blurRadius: 16.0,
        offset: const Offset(0, 0),
      ),
      Shadow(
        color: color3,
        blurRadius: 24.0,
        offset: const Offset(0, 0),
      ),
    ];
  }

  // Helper method to adjust color opacity without using deprecated methods
  static Color _adjustColorOpacity(Color color, double opacity) {
    final int alpha = (opacity * 255).round();
    // Use hardcoded values for specific colors to avoid deprecation warnings
    if (color == neonBlue) return Color.fromARGB(alpha, 0, 229, 255);
    if (color == neonPink) return Color.fromARGB(alpha, 255, 0, 229);
    if (color == neonPurple) return Color.fromARGB(alpha, 170, 0, 255);
    if (color == neonGreen) return Color.fromARGB(alpha, 0, 230, 118);
    if (color == neonYellow) return Color.fromARGB(alpha, 255, 234, 0);
    if (color == neonRed) return Color.fromARGB(alpha, 255, 23, 68);
    if (color == neonCyan) return Color.fromARGB(alpha, 24, 255, 255);
    if (color == neonOrange) return Color.fromARGB(alpha, 255, 145, 0);
    if (color == neonTeal) return Color.fromARGB(alpha, 29, 233, 182);
    if (color == neonLime) return Color.fromARGB(alpha, 198, 255, 0);
    if (color == vaporwavePink) return Color.fromARGB(alpha, 255, 106, 213);
    if (color == vaporwaveBlue) return Color.fromARGB(alpha, 38, 208, 206);
    if (color == retroWaveRed) return Color.fromARGB(alpha, 255, 46, 99);
    if (color == retroWaveBlue) return Color.fromARGB(alpha, 37, 42, 52);
    if (color == retroWavePurple) return Color.fromARGB(alpha, 130, 54, 203);
    if (color == mintGreen) return Color.fromARGB(alpha, 152, 221, 202);
    if (color == pastelLavender) return Color.fromARGB(alpha, 212, 187, 252);
    if (color == pastelPink) return Color.fromARGB(alpha, 255, 182, 193);
    if (color == pastelBlue) return Color.fromARGB(alpha, 173, 216, 230);
    if (color == pastelYellow) return Color.fromARGB(alpha, 255, 250, 205);
    if (color == modernGold) return Color.fromARGB(alpha, 255, 215, 0);
    if (color == modernSilver) return Color.fromARGB(alpha, 192, 192, 192);
    if (color == modernRose) return Color.fromARGB(alpha, 255, 127, 127);
    if (color == modernSky) return Color.fromARGB(alpha, 135, 206, 235);
    if (color == modernMint) return Color.fromARGB(alpha, 152, 251, 152);

    // For any other color, use a fixed approach
    // Default to a semi-transparent white if color is not recognized
    return Color.fromARGB(alpha, 255, 255, 255);
  }

  // Gradient presets
  static LinearGradient neonBorderGradient(Color startColor, Color endColor) {
    return LinearGradient(
      begin: Alignment.topLeft,
      end: Alignment.bottomRight,
      colors: [
        startColor,
        endColor,
      ],
    );
  }
}