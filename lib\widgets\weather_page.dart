import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/weather_provider.dart';
import '../providers/theme_provider.dart';

class WeatherPage extends StatelessWidget {
  final bool isLandscape;

  const WeatherPage({
    Key? key,
    required this.isLandscape,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final weatherProvider = Provider.of<WeatherProvider>(context);
    final themeProvider = Provider.of<ThemeProvider>(context);

    return isLandscape
        ? _buildLandscapeLayout(context, weatherProvider, themeProvider)
        : _buildPortraitLayout(context, weatherProvider, themeProvider);
  }

  Widget _buildPortraitLayout(
    BuildContext context,
    WeatherProvider weatherProvider,
    ThemeProvider themeProvider,
  ) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 24.0, vertical: 32.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          // Page title
          Text(
            'Weather Information',
            style: TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: themeProvider.textColor,
            ),
          ),
          const SizedBox(height: 24),

          // Weather content
          Expanded(
            child: _buildWeatherContent(context, weatherProvider, themeProvider),
          ),
        ],
      ),
    );
  }

  Widget _buildLandscapeLayout(
    BuildContext context,
    WeatherProvider weatherProvider,
    ThemeProvider themeProvider,
  ) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 32.0, vertical: 24.0),
      child: Row(
        children: [
          // Left side - Current weather
          Expanded(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                // Location and temperature
                if (weatherProvider.weather != null) ...[
                  Text(
                    weatherProvider.weather!.location,
                    style: TextStyle(
                      fontSize: 24,
                      fontWeight: FontWeight.bold,
                      color: themeProvider.textColor,
                    ),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 16),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(
                        _getWeatherIcon(weatherProvider),
                        color: themeProvider.textColor,
                        size: 48,
                      ),
                      const SizedBox(width: 16),
                      Text(
                        weatherProvider.weather!.temperature,
                        style: TextStyle(
                          fontSize: 48,
                          fontWeight: FontWeight.w300,
                          color: themeProvider.textColor,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 8),
                  Text(
                    weatherProvider.weather!.condition,
                    style: TextStyle(
                      fontSize: 20,
                      color: themeProvider.secondaryTextColor,
                    ),
                  ),
                ] else if (weatherProvider.isLoading) ...[
                  CircularProgressIndicator(color: themeProvider.textColor),
                ] else if (weatherProvider.error != null) ...[
                  Icon(Icons.error_outline, color: Colors.red.shade300, size: 48),
                  const SizedBox(height: 16),
                  Text(
                    'Error loading weather data',
                    style: TextStyle(color: Colors.red.shade300),
                  ),
                ],
              ],
            ),
          ),

          // Right side - Weather details
          Expanded(
            child: weatherProvider.weather != null
                ? _buildWeatherDetails(weatherProvider, themeProvider)
                : const SizedBox(),
          ),
        ],
      ),
    );
  }

  Widget _buildWeatherContent(
    BuildContext context,
    WeatherProvider weatherProvider,
    ThemeProvider themeProvider,
  ) {
    if (weatherProvider.isLoading) {
      return Center(
        child: CircularProgressIndicator(color: themeProvider.textColor),
      );
    }

    if (weatherProvider.error != null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.error_outline, color: Colors.red.shade300, size: 48),
            const SizedBox(height: 16),
            Text(
              'Error loading weather data',
              style: TextStyle(color: Colors.red.shade300),
            ),
            const SizedBox(height: 24),
            ElevatedButton(
              onPressed: () => weatherProvider.fetchWeather(),
              style: ElevatedButton.styleFrom(
                backgroundColor: themeProvider.primaryColor,
                foregroundColor: Colors.white,
              ),
              child: const Text('Retry'),
            ),
          ],
        ),
      );
    }

    if (weatherProvider.weather == null) {
      return Center(
        child: Text(
          'No weather data available',
          style: TextStyle(color: themeProvider.secondaryTextColor),
        ),
      );
    }

    final weather = weatherProvider.weather!;

    return SingleChildScrollView(
      child: Column(
        children: [
          // Location
          Text(
            weather.location,
            style: TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: themeProvider.textColor,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 24),

          // Current weather
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                _getWeatherIcon(weatherProvider),
                color: themeProvider.textColor,
                size: 64,
              ),
              const SizedBox(width: 16),
              Text(
                weather.temperature,
                style: TextStyle(
                  fontSize: 64,
                  fontWeight: FontWeight.w300,
                  color: themeProvider.textColor,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Text(
            weather.condition,
            style: TextStyle(
              fontSize: 24,
              color: themeProvider.secondaryTextColor,
            ),
          ),
          const SizedBox(height: 40),

          // Weather details
          _buildWeatherDetails(weatherProvider, themeProvider),
        ],
      ),
    );
  }

  Widget _buildWeatherDetails(
    WeatherProvider weatherProvider,
    ThemeProvider themeProvider,
  ) {
    final weather = weatherProvider.weather!;

    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: themeProvider.textColor.withAlpha(20),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: themeProvider.textColor.withAlpha(40),
          width: 1,
        ),
      ),
      child: Column(
        children: [
          _buildDetailRow(Icons.thermostat, 'Feels Like', weather.feelsLike, themeProvider),
          Divider(height: 24, color: themeProvider.textColor.withAlpha(40)),
          _buildDetailRow(Icons.water_drop, 'Humidity', weather.humidity, themeProvider),
          Divider(height: 24, color: themeProvider.textColor.withAlpha(40)),
          _buildDetailRow(Icons.air, 'Wind Speed', weather.windSpeed, themeProvider),
        ],
      ),
    );
  }

  Widget _buildDetailRow(
    IconData icon,
    String label,
    String value,
    ThemeProvider themeProvider,
  ) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8.0),
      child: Row(
        children: [
          Icon(icon, color: themeProvider.secondaryTextColor, size: 24),
          const SizedBox(width: 16),
          Expanded(
            child: Text(
              label,
              style: TextStyle(
                fontSize: 16,
                color: themeProvider.secondaryTextColor,
              ),
            ),
          ),
          Text(
            value,
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: themeProvider.textColor,
            ),
          ),
        ],
      ),
    );
  }

  IconData _getWeatherIcon(WeatherProvider weatherProvider) {
    if (weatherProvider.weather == null) return Icons.cloud_off;

    switch (weatherProvider.weather!.condition.toLowerCase()) {
      case 'sunny':
      case 'clear':
        return Icons.wb_sunny;
      case 'partly cloudy':
        return Icons.cloud_queue;
      case 'cloudy':
        return Icons.cloud;
      case 'rainy':
      case 'rain':
        return Icons.water_drop;
      case 'thunderstorm':
        return Icons.flash_on;
      case 'snowy':
      case 'snow':
        return Icons.ac_unit;
      case 'foggy':
      case 'fog':
        return Icons.cloud;
      default:
        return Icons.cloud;
    }
  }
}
