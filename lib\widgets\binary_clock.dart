import 'package:flutter/material.dart';

class BinaryClock extends StatelessWidget {
  final DateTime time;
  final Color activeColor;
  final Color inactiveColor;
  final Color backgroundColor;
  final double size;
  final bool showLabels;
  final String? fontFamily;

  const BinaryClock({
    Key? key,
    required this.time,
    this.activeColor = Colors.blue,
    this.inactiveColor = Colors.grey,
    this.backgroundColor = Colors.black,
    this.size = 300,
    this.showLabels = true,
    this.fontFamily,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    // Extract time components
    final hour = time.hour;
    final minute = time.minute;
    final second = time.second;

    // Convert to binary representation
    final hourTens = hour ~/ 10;
    final hourOnes = hour % 10;
    final minuteTens = minute ~/ 10;
    final minuteOnes = minute % 10;
    final secondTens = second ~/ 10;
    final secondOnes = second % 10;

    return Container(
      width: size,
      padding: EdgeInsets.all(size * 0.05),
      decoration: BoxDecoration(
        color: backgroundColor,
        borderRadius: BorderRadius.circular(16),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          if (showLabels)
            Padding(
              padding: EdgeInsets.only(bottom: size * 0.05),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                children: [
                  _buildLabel('Hours'),
                  _buildLabel('Minutes'),
                  _buildLabel('Seconds'),
                ],
              ),
            ),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              // Hours
              Column(
                children: [
                  _buildBinaryColumn(hourTens, 2),
                  SizedBox(height: size * 0.02),
                  _buildBinaryColumn(hourOnes, 4),
                ],
              ),
              // Minutes
              Column(
                children: [
                  _buildBinaryColumn(minuteTens, 3),
                  SizedBox(height: size * 0.02),
                  _buildBinaryColumn(minuteOnes, 4),
                ],
              ),
              // Seconds
              Column(
                children: [
                  _buildBinaryColumn(secondTens, 3),
                  SizedBox(height: size * 0.02),
                  _buildBinaryColumn(secondOnes, 4),
                ],
              ),
            ],
          ),
          if (showLabels)
            Padding(
              padding: EdgeInsets.only(top: size * 0.05),
              child: Text(
                '${_formatDigit(hour)}:${_formatDigit(minute)}:${_formatDigit(second)}',
                style: TextStyle(
                  color: activeColor,
                  fontSize: size * 0.08,
                  fontWeight: FontWeight.bold,
                  fontFamily: fontFamily,
                ),
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildLabel(String text) {
    return Text(
      text,
      style: TextStyle(
        color: activeColor,
        fontSize: size * 0.05,
        fontWeight: FontWeight.bold,
        fontFamily: fontFamily,
      ),
    );
  }

  Widget _buildBinaryColumn(int value, int bits) {
    final binaryDigits = _convertToBinary(value, bits);

    return Column(
      children: [
        for (int i = 0; i < bits; i++)
          Padding(
            padding: EdgeInsets.symmetric(vertical: size * 0.01),
            child: _buildBinaryDot(binaryDigits[i] == 1),
          ),
      ],
    );
  }

  Widget _buildBinaryDot(bool isActive) {
    return Container(
      width: size * 0.08,
      height: size * 0.08,
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        color: isActive ? activeColor : inactiveColor,
        boxShadow: isActive
            ? [
                BoxShadow(
                  color: activeColor.withAlpha(128),
                  blurRadius: 8,
                  spreadRadius: 2,
                ),
              ]
            : null,
      ),
    );
  }

  List<int> _convertToBinary(int value, int bits) {
    final result = List<int>.filled(bits, 0);

    for (int i = 0; i < bits; i++) {
      result[bits - i - 1] = (value >> i) & 1;
    }

    return result;
  }

  String _formatDigit(int value) {
    return value.toString().padLeft(2, '0');
  }
}
