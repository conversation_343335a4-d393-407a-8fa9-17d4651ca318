import 'package:flutter/material.dart';

enum ClockStyle {
  digital,
  digitalWithSeconds,
  analogBasic,
  analogModern,
  analogMinimal,
  binaryClock,
}

class ClockStyleProvider extends ChangeNotifier {
  // Current clock style
  ClockStyle _currentStyle = ClockStyle.digitalWithSeconds;

  // 24-hour format
  bool _is24HourFormat = false;

  // Show date
  bool _showDate = true;

  // Show seconds
  bool _showSeconds = true;

  // Show day of week
  bool _showDayOfWeek = true;

  // Getters
  ClockStyle get currentStyle => _currentStyle;
  bool get is24HourFormat => _is24HourFormat;
  bool get showDate => _showDate;
  bool get showSeconds => _showSeconds;
  bool get showDayOfWeek => _showDayOfWeek;

  // Get all available clock styles
  List<ClockStyle> get availableStyles => ClockStyle.values;

  // Get name for a clock style
  String getStyleName(ClockStyle style) {
    switch (style) {
      case ClockStyle.digital:
        return 'Digital';
      case ClockStyle.digitalWithSeconds:
        return 'Digital with Seconds';
      case ClockStyle.analogBasic:
        return 'Analog Basic';
      case ClockStyle.analogModern:
        return 'Analog Modern';
      case ClockStyle.analogMinimal:
        return 'Analog Minimal';
      case ClockStyle.binaryClock:
        return 'Binary Clock';
    }
  }

  // Set clock style
  void setClockStyle(ClockStyle style) {
    _currentStyle = style;

    // Update seconds visibility based on style
    if (style == ClockStyle.digitalWithSeconds) {
      _showSeconds = true;
    } else if (style == ClockStyle.digital) {
      _showSeconds = false;
    }

    notifyListeners();
  }

  // Toggle 24-hour format
  void toggle24HourFormat() {
    _is24HourFormat = !_is24HourFormat;
    notifyListeners();
  }

  // Set 24-hour format
  void set24HourFormat(bool value) {
    _is24HourFormat = value;
    notifyListeners();
  }

  // Toggle date visibility
  void toggleShowDate() {
    _showDate = !_showDate;
    notifyListeners();
  }

  // Set date visibility
  void setShowDate(bool value) {
    _showDate = value;
    notifyListeners();
  }

  // Toggle seconds visibility
  void toggleShowSeconds() {
    _showSeconds = !_showSeconds;

    // Update clock style based on seconds visibility
    if (_currentStyle == ClockStyle.digital && _showSeconds) {
      _currentStyle = ClockStyle.digitalWithSeconds;
    } else if (_currentStyle == ClockStyle.digitalWithSeconds && !_showSeconds) {
      _currentStyle = ClockStyle.digital;
    }

    notifyListeners();
  }

  // Set seconds visibility
  void setShowSeconds(bool value) {
    _showSeconds = value;

    // Update clock style based on seconds visibility
    if (_currentStyle == ClockStyle.digital && value) {
      _currentStyle = ClockStyle.digitalWithSeconds;
    } else if (_currentStyle == ClockStyle.digitalWithSeconds && !value) {
      _currentStyle = ClockStyle.digital;
    }

    notifyListeners();
  }

  // Toggle day of week visibility
  void toggleShowDayOfWeek() {
    _showDayOfWeek = !_showDayOfWeek;
    notifyListeners();
  }

  // Set day of week visibility
  void setShowDayOfWeek(bool value) {
    _showDayOfWeek = value;
    notifyListeners();
  }
}
