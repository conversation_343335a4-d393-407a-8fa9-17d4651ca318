import 'package:flutter/material.dart';

/// A utility class that provides responsive sizing methods
class ResponsiveSize {
  /// Get a responsive width based on screen size
  static double getWidth(BuildContext context, double percentage) {
    return MediaQuery.of(context).size.width * percentage;
  }

  /// Get a responsive height based on screen size
  static double getHeight(BuildContext context, double percentage) {
    return MediaQuery.of(context).size.height * percentage;
  }

  /// Get a responsive font size based on screen size
  static double getFontSize(BuildContext context, double baseSize) {
    final screenWidth = MediaQuery.of(context).size.width;
    final screenHeight = MediaQuery.of(context).size.height;
    
    // Use the smaller dimension to calculate font size
    final smallerDimension = screenWidth < screenHeight ? screenWidth : screenHeight;
    
    // Scale font size based on screen size
    if (smallerDimension < 320) {
      return baseSize * 0.8; // Small phones
    } else if (smallerDimension < 480) {
      return baseSize * 0.9; // Normal phones
    } else if (smallerDimension < 768) {
      return baseSize; // Large phones
    } else if (smallerDimension < 1024) {
      return baseSize * 1.1; // Tablets
    } else {
      return baseSize * 1.2; // Large tablets and desktops
    }
  }

  /// Get a responsive size for widgets based on screen size
  static double getSize(BuildContext context, double baseSize) {
    final screenWidth = MediaQuery.of(context).size.width;
    final screenHeight = MediaQuery.of(context).size.height;
    
    // Use the smaller dimension to calculate size
    final smallerDimension = screenWidth < screenHeight ? screenWidth : screenHeight;
    
    // Scale size based on screen size
    if (smallerDimension < 320) {
      return baseSize * 0.8; // Small phones
    } else if (smallerDimension < 480) {
      return baseSize * 0.9; // Normal phones
    } else if (smallerDimension < 768) {
      return baseSize; // Large phones
    } else if (smallerDimension < 1024) {
      return baseSize * 1.1; // Tablets
    } else {
      return baseSize * 1.2; // Large tablets and desktops
    }
  }

  /// Check if the device is in landscape mode
  static bool isLandscape(BuildContext context) {
    return MediaQuery.of(context).orientation == Orientation.landscape;
  }

  /// Get a responsive padding based on screen size
  static EdgeInsets getPadding(BuildContext context, {
    double horizontal = 0.05, 
    double vertical = 0.03
  }) {
    return EdgeInsets.symmetric(
      horizontal: getWidth(context, horizontal),
      vertical: getHeight(context, vertical),
    );
  }
  
  /// Get a safe area padding
  static EdgeInsets getSafeAreaPadding(BuildContext context) {
    return MediaQuery.of(context).padding;
  }
}
