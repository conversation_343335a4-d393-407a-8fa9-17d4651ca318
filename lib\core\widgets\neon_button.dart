import 'package:flutter/material.dart';
import '../theme/neon_colors.dart';

class NeonButton extends StatelessWidget {
  final Color color;
  final VoidCallback onPressed;
  final Widget child;
  final double glowIntensity;

  const NeonButton({
    super.key,
    required this.color,
    required this.onPressed,
    required this.child,
    this.glowIntensity = 0.5,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onPressed,
      child: Container(
        padding: const EdgeInsets.all(8.0),
        decoration: BoxDecoration(
          color: Colors.transparent,
          borderRadius: BorderRadius.circular(8.0),
          border: Border.all(
            color: color,
            width: 2.0,
          ),
          boxShadow: NeonColors.neonGlow(color, intensity: glowIntensity),
        ),
        child: child,
      ),
    );
  }
} 