import 'package:flutter/material.dart';
import 'dart:async';

class FlipPanelClock extends StatefulWidget {
  final bool showSeconds;
  final bool is24HourFormat;
  final Color digitColor;
  final Color backgroundColor;
  final Color dividerColor;
  final double digitWidth;
  final double digitHeight;
  final double spacing;

  const FlipPanelClock({
    Key? key,
    this.showSeconds = true,
    this.is24HourFormat = false,
    this.digitColor = Colors.white,
    this.backgroundColor = const Color(0xFF1C1B1F),
    this.dividerColor = const Color(0xFF49454F),
    this.digitWidth = 60,
    this.digitHeight = 90,
    this.spacing = 8,
  }) : super(key: key);

  @override
  State<FlipPanelClock> createState() => _FlipPanelClockState();
}

class _FlipPanelClockState extends State<FlipPanelClock> {
  late Timer _timer;
  late DateTime _currentTime;
  late String _hourTens = '0';
  late String _hourOnes = '0';
  late String _minuteTens = '0';
  late String _minuteOnes = '0';
  late String _secondTens = '0';
  late String _secondOnes = '0';
  late String _period = 'AM';

  @override
  void initState() {
    super.initState();
    _updateTime();
    _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      setState(() {
        _updateTime();
      });
    });
  }

  @override
  void dispose() {
    _timer.cancel();
    super.dispose();
  }

  void _updateTime() {
    _currentTime = DateTime.now();

    // Format hour based on 12/24 hour setting
    int hour = _currentTime.hour;
    if (!widget.is24HourFormat) {
      _period = hour >= 12 ? 'PM' : 'AM';
      hour = hour % 12;
      if (hour == 0) hour = 12;
    }

    // Split digits
    final hourStr = hour.toString().padLeft(2, '0');
    final minuteStr = _currentTime.minute.toString().padLeft(2, '0');
    final secondStr = _currentTime.second.toString().padLeft(2, '0');

    _hourTens = hourStr[0];
    _hourOnes = hourStr[1];
    _minuteTens = minuteStr[0];
    _minuteOnes = minuteStr[1];
    _secondTens = secondStr[0];
    _secondOnes = secondStr[1];
  }

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        // Hours
        _buildDigitPair(_hourTens, _hourOnes),

        // Separator
        _buildSeparator(),

        // Minutes
        _buildDigitPair(_minuteTens, _minuteOnes),

        // Seconds (optional)
        if (widget.showSeconds) ...[
          _buildSeparator(),
          _buildDigitPair(_secondTens, _secondOnes),
        ],

        // AM/PM indicator (for 12-hour format)
        if (!widget.is24HourFormat) ...[
          SizedBox(width: widget.spacing * 2),
          _buildPeriodIndicator(),
        ],
      ],
    );
  }

  Widget _buildDigitPair(String tens, String ones) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        _buildDigit(tens),
        SizedBox(width: widget.spacing / 2),
        _buildDigit(ones),
      ],
    );
  }

  Widget _buildDigit(String digit) {
    return Container(
      width: widget.digitWidth,
      height: widget.digitHeight,
      decoration: BoxDecoration(
        color: widget.backgroundColor,
        borderRadius: BorderRadius.circular(8),
        boxShadow: const [
          BoxShadow(
            color: Color(0x4D000000), // 0.3 opacity black
            blurRadius: 4,
            offset: Offset(0, 2),
          ),
        ],
      ),
      child: Stack(
        children: [
          // Top half
          Positioned(
            top: 0,
            left: 0,
            right: 0,
            height: widget.digitHeight / 2,
            child: Container(
              decoration: BoxDecoration(
                color: widget.backgroundColor,
                borderRadius: const BorderRadius.vertical(top: Radius.circular(8)),
              ),
              child: Center(
                child: Text(
                  digit,
                  style: TextStyle(
                    fontSize: widget.digitHeight * 0.6,
                    fontWeight: FontWeight.bold,
                    color: widget.digitColor,
                  ),
                ),
              ),
            ),
          ),

          // Dividing line
          Positioned(
            top: widget.digitHeight / 2 - 0.5,
            left: 0,
            right: 0,
            height: 1,
            child: Container(color: widget.dividerColor),
          ),

          // Bottom half
          Positioned(
            top: widget.digitHeight / 2,
            left: 0,
            right: 0,
            bottom: 0,
            child: Container(
              decoration: BoxDecoration(
                color: widget.backgroundColor,
                borderRadius: const BorderRadius.vertical(bottom: Radius.circular(8)),
              ),
              child: Center(
                child: Text(
                  digit,
                  style: TextStyle(
                    fontSize: widget.digitHeight * 0.6,
                    fontWeight: FontWeight.bold,
                    color: widget.digitColor,
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSeparator() {
    return SizedBox(
      width: widget.spacing * 2,
      child: Center(
        child: Text(
          ':',
          style: TextStyle(
            fontSize: widget.digitHeight * 0.6,
            fontWeight: FontWeight.bold,
            color: widget.digitColor,
          ),
        ),
      ),
    );
  }

  Widget _buildPeriodIndicator() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: widget.backgroundColor,
        borderRadius: BorderRadius.circular(8),
        boxShadow: const [
          BoxShadow(
            color: Color(0x4D000000), // 0.3 opacity black
            blurRadius: 4,
            offset: Offset(0, 2),
          ),
        ],
      ),
      child: Text(
        _period,
        style: TextStyle(
          fontSize: widget.digitHeight * 0.3,
          fontWeight: FontWeight.bold,
          color: widget.digitColor,
        ),
      ),
    );
  }
}
