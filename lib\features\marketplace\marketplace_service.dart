import 'package:cloud_firestore/cloud_firestore.dart';
import '../../models/theme_model.dart';

class MarketplaceService {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  // Get all available themes
  Future<List<ThemeModel>> getThemes() async {
    try {
      final snapshot = await _firestore.collection('themes').get();
      return snapshot.docs
          .map((doc) => ThemeModel.fromMap({...doc.data(), 'id': doc.id}))
          .toList();
    } catch (e) {
      throw Exception('Failed to fetch themes: $e');
    }
  }

  // Purchase a theme
  Future<void> purchaseTheme(String themeId, String userId) async {
    try {
      // Start a batch write
      final batch = _firestore.batch();

      // Get theme reference
      final themeRef = _firestore.collection('themes').doc(themeId);
      final themeDoc = await themeRef.get();
      if (!themeDoc.exists) {
        throw Exception('Theme not found');
      }

      // Get user reference
      final userRef = _firestore.collection('users').doc(userId);
      final userDoc = await userRef.get();
      if (!userDoc.exists) {
        throw Exception('User not found');
      }

      // Update theme downloads
      batch.update(themeRef, {
        'downloads': FieldValue.increment(1),
      });

      // Add theme to user's purchased themes
      batch.update(userRef, {
        'purchasedThemes': FieldValue.arrayUnion([themeId]),
      });

      // Commit the batch
      await batch.commit();
    } catch (e) {
      throw Exception('Failed to purchase theme: $e');
    }
  }

  // Upload a new theme
  Future<void> uploadTheme(ThemeModel theme) async {
    try {
      await _firestore.collection('themes').add(theme.toMap());
    } catch (e) {
      throw Exception('Failed to upload theme: $e');
    }
  }

  // Update an existing theme
  Future<void> updateTheme(ThemeModel theme) async {
    try {
      await _firestore.collection('themes').doc(theme.id).update(theme.toMap());
    } catch (e) {
      throw Exception('Failed to update theme: $e');
    }
  }

  // Delete a theme
  Future<void> deleteTheme(String themeId) async {
    try {
      await _firestore.collection('themes').doc(themeId).delete();
    } catch (e) {
      throw Exception('Failed to delete theme: $e');
    }
  }
} 