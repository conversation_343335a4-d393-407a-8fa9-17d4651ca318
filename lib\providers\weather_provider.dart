import 'dart:async';
import 'package:flutter/material.dart';

class Weather {
  final String temperature;
  final String condition;
  final String location;
  final String humidity;
  final String windSpeed;
  final String feelsLike;
  final String icon;

  Weather({
    required this.temperature,
    required this.condition,
    required this.location,
    required this.humidity,
    required this.windSpeed,
    required this.feelsLike,
    required this.icon,
  });
}

class WeatherProvider extends ChangeNotifier {
  Weather? _weather;
  String? _error;
  bool _isLoading = false;
  late Timer _refreshTimer;
  String _location = 'Agra, India'; // Default location

  Weather? get weather => _weather;
  String? get error => _error;
  bool get isLoading => _isLoading;
  String get location => _location;

  WeatherProvider() {
    fetchWeather();
    // Refresh weather data every 30 minutes
    _refreshTimer = Timer.periodic(const Duration(minutes: 30), (timer) {
      fetchWeather();
    });
  }

  void setLocation(String newLocation) {
    _location = newLocation;
    fetchWeather();
  }

  Future<void> fetchWeather() async {
    _isLoading = true;
    _error = null;
    notifyListeners();

    try {
      // For demo purposes, we'll use a mock API response
      // In a real app, you would use a weather API like OpenWeatherMap
      await Future.delayed(const Duration(seconds: 2)); // Simulate network delay

      // Mock weather data
      final weatherData = {
        'temperature': '28°C',
        'condition': 'Sunny',
        'location': _location,
        'humidity': '65%',
        'windSpeed': '12 km/h',
        'feelsLike': '30°C',
        'icon': 'clear_day',
      };

      _weather = Weather(
        temperature: weatherData['temperature']!,
        condition: weatherData['condition']!,
        location: weatherData['location']!,
        humidity: weatherData['humidity']!,
        windSpeed: weatherData['windSpeed']!,
        feelsLike: weatherData['feelsLike']!,
        icon: weatherData['icon']!,
      );
    } catch (e) {
      _error = 'Failed to load weather data: ${e.toString()}';
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  IconData getWeatherIcon() {
    if (_weather == null) return Icons.cloud_off;

    switch (_weather!.icon) {
      case 'clear_day':
        return Icons.wb_sunny;
      case 'clear_night':
        return Icons.nightlight_round;
      case 'cloudy':
        return Icons.cloud;
      case 'partly_cloudy_day':
        return Icons.cloud_queue;
      case 'partly_cloudy_night':
        return Icons.nights_stay;
      case 'rain':
        return Icons.water_drop;
      case 'snow':
        return Icons.ac_unit;
      case 'thunderstorm':
        return Icons.flash_on;
      case 'fog':
        return Icons.cloud;
      default:
        return Icons.cloud;
    }
  }

  @override
  void dispose() {
    _refreshTimer.cancel();
    super.dispose();
  }
}