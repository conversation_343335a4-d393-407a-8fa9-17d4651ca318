import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/battery_provider.dart';
import '../providers/weather_provider.dart';
import '../providers/theme_provider.dart';
import '../providers/clock_style_provider.dart';
import '../providers/font_provider.dart';
import '../widgets/clock_page.dart';
import '../widgets/weather_page.dart';
import '../widgets/settings_page.dart';

class HomeScreen extends StatefulWidget {
  const HomeScreen({Key? key}) : super(key: key);

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {
  final PageController _pageController = PageController();
  int _currentPage = 0;

  @override
  void initState() {
    super.initState();

    // Initialize providers
    WidgetsBinding.instance.addPostFrameCallback((_) {
      Provider.of<BatteryProvider>(context, listen: false);
      Provider.of<WeatherProvider>(context, listen: false).fetchWeather();
      Provider.of<ThemeProvider>(context, listen: false);
      Provider.of<ClockStyleProvider>(context, listen: false);
      Provider.of<FontProvider>(context, listen: false);
    });
  }

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    // Get screen orientation and screen size
    final isLandscape = MediaQuery.of(context).orientation == Orientation.landscape;
    final screenSize = MediaQuery.of(context).size;
    final themeProvider = Provider.of<ThemeProvider>(context);

    return Scaffold(
      backgroundColor: themeProvider.backgroundColor,
      body: Stack(
        children: [
          // Background gradient
          Container(
            width: screenSize.width,
            height: screenSize.height,
            decoration: BoxDecoration(
              gradient: themeProvider.backgroundGradient,
            ),
          ),

          // Main content with PageView for swiping
          PageView(
            controller: _pageController,
            onPageChanged: (index) {
              setState(() {
                _currentPage = index;
              });
            },
            children: [
              // Clock page
              ClockPage(isLandscape: isLandscape),

              // Weather page
              WeatherPage(isLandscape: isLandscape),

              // Settings page
              SettingsPage(isLandscape: isLandscape),
            ],
          ),

          // Page indicator
          Positioned(
            bottom: 16,
            left: 0,
            right: 0,
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                _buildPageIndicator(0),
                const SizedBox(width: 12),
                _buildPageIndicator(1),
                const SizedBox(width: 12),
                _buildPageIndicator(2),
              ],
            ),
          ),

          // Navigation hints
          _buildNavigationHint(),
        ],
      ),
    );
  }

  Widget _buildPageIndicator(int pageIndex) {
    final themeProvider = Provider.of<ThemeProvider>(context);
    return Container(
      width: 8,
      height: 8,
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        color: _currentPage == pageIndex
            ? themeProvider.textColor
            : themeProvider.textColor.withAlpha(102), // 0.4 * 255 = 102
      ),
    );
  }

  Widget _buildNavigationHint() {
    final themeProvider = Provider.of<ThemeProvider>(context);

    // Clock page hints
    if (_currentPage == 0) {
      return Positioned(
        top: 16,
        right: 16,
        child: Row(
          children: [
            Text(
              'Swipe for weather',
              style: TextStyle(
                color: themeProvider.textColor.withAlpha(179),
                fontSize: 14,
              ),
            ),
            const SizedBox(width: 4),
            Icon(
              Icons.arrow_forward_ios,
              color: themeProvider.textColor.withAlpha(179),
              size: 14,
            ),
          ],
        ),
      );
    }

    // Weather page hints
    else if (_currentPage == 1) {
      return Stack(
        children: [
          // Left hint (back to clock)
          Positioned(
            top: 16,
            left: 16,
            child: Row(
              children: [
                Icon(
                  Icons.arrow_back_ios,
                  color: themeProvider.textColor.withAlpha(179),
                  size: 14,
                ),
                const SizedBox(width: 4),
                Text(
                  'Clock',
                  style: TextStyle(
                    color: themeProvider.textColor.withAlpha(179),
                    fontSize: 14,
                  ),
                ),
              ],
            ),
          ),

          // Right hint (to settings)
          Positioned(
            top: 16,
            right: 16,
            child: Row(
              children: [
                Text(
                  'Settings',
                  style: TextStyle(
                    color: themeProvider.textColor.withAlpha(179),
                    fontSize: 14,
                  ),
                ),
                const SizedBox(width: 4),
                Icon(
                  Icons.arrow_forward_ios,
                  color: themeProvider.textColor.withAlpha(179),
                  size: 14,
                ),
              ],
            ),
          ),
        ],
      );
    }

    // Settings page hints
    else {
      return Positioned(
        top: 16,
        left: 16,
        child: Row(
          children: [
            Icon(
              Icons.arrow_back_ios,
              color: themeProvider.textColor.withAlpha(179),
              size: 14,
            ),
            const SizedBox(width: 4),
            Text(
              'Weather',
              style: TextStyle(
                color: themeProvider.textColor.withAlpha(179),
                fontSize: 14,
              ),
            ),
          ],
        ),
      );
    }
  }
}
