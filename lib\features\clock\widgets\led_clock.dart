import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:weather_icons/weather_icons.dart';
import '../../../../providers/weather_provider.dart';
import '../../../../providers/font_provider.dart';
import '../../../../providers/theme_provider.dart';
import '../../../../core/theme/neon_colors.dart';
import '../../../../core/widgets/neon_container.dart';

class LEDClock extends StatefulWidget {
  final DateTime time;
  final Color color;
  final double size;
  final bool showSeconds;
  final bool showDate;
  final bool showWeather;
  final double glowIntensity;

  const LEDClock({
    super.key,
    required this.time,
    required this.color,
    required this.size,
    required this.showSeconds,
    required this.showDate,
    required this.showWeather,
    required this.glowIntensity,
  });

  @override
  State<LEDClock> createState() => _LEDClockState();
}

class _LEDClockState extends State<LEDClock> with SingleTickerProviderStateMixin {
  late AnimationController _glowController;
  late Animation<double> _glowAnimation;

  @override
  void initState() {
    super.initState();
    _glowController = AnimationController(
      vsync: this,
      duration: const Duration(seconds: 2),
    )..repeat(reverse: true);

    _glowAnimation = Tween<double>(
      begin: widget.glowIntensity * 0.5,
      end: widget.glowIntensity,
    ).animate(_glowController);
  }

  @override
  void dispose() {
    _glowController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final fontProvider = Provider.of<FontProvider>(context);
    final weatherProvider = Provider.of<WeatherProvider>(context);
    final themeProvider = Provider.of<ThemeProvider>(context);

    return AnimatedBuilder(
      animation: _glowAnimation,
      builder: (context, child) {
        return NeonContainer(
          borderColor: widget.color,
          glowIntensity: _glowAnimation.value,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // Time display with animation
              Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  _buildDigit(_formatHour(widget.time, themeProvider.is24HourFormat)[0], fontProvider.selectedFont),
                  _buildDigit(_formatHour(widget.time, themeProvider.is24HourFormat)[1], fontProvider.selectedFont),
                  _buildColon(fontProvider.selectedFont),
                  _buildDigit(widget.time.minute.toString().padLeft(2, '0')[0], fontProvider.selectedFont),
                  _buildDigit(widget.time.minute.toString().padLeft(2, '0')[1], fontProvider.selectedFont),
                  if (widget.showSeconds) ...[
                    _buildColon(fontProvider.selectedFont),
                    _buildDigit(widget.time.second.toString().padLeft(2, '0')[0], fontProvider.selectedFont),
                    _buildDigit(widget.time.second.toString().padLeft(2, '0')[1], fontProvider.selectedFont),
                  ],
                  if (!themeProvider.is24HourFormat) ...[
                    const SizedBox(width: 8),
                    Text(
                      widget.time.hour >= 12 ? 'PM' : 'AM',
                      style: TextStyle(
                        color: widget.color,
                        fontSize: widget.size * 0.4,
                        fontFamily: fontProvider.selectedFont,
                        shadows: NeonColors.textGlow(widget.color, intensity: _glowAnimation.value),
                      ),
                    ),
                  ],
                ],
              ),
              if (widget.showDate) ...[
                const SizedBox(height: 16),
                Text(
                  _formatDate(widget.time),
                  style: TextStyle(
                    color: widget.color,
                    fontSize: widget.size * 0.4,
                    fontFamily: fontProvider.selectedFont,
                    letterSpacing: 2,
                    shadows: NeonColors.textGlow(widget.color, intensity: _glowAnimation.value),
                  ),
                ),
              ],
              if (widget.showWeather) ...[
                const SizedBox(height: 16),
                if (weatherProvider.isLoading)
                  CircularProgressIndicator(
                    color: widget.color,
                    strokeWidth: 2,
                  )
                else if (weatherProvider.error?.isNotEmpty ?? false)
                  Text(
                    weatherProvider.error!,
                    style: TextStyle(
                      color: Colors.red,
                      fontSize: widget.size * 0.3,
                      fontFamily: fontProvider.selectedFont,
                      shadows: NeonColors.textGlow(widget.color, intensity: _glowAnimation.value * 0.5),
                    ),
                  )
                else if (weatherProvider.weather != null)
                  NeonContainer(
                    borderColor: widget.color,
                    glowIntensity: _glowAnimation.value * 0.3,
                    padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Icon(
                              _getWeatherIcon(weatherProvider.weather!.condition),
                              color: widget.color,
                              size: widget.size * 0.6,
                            ),
                            const SizedBox(width: 16),
                            Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  '${weatherProvider.weather!.temperature}°C',
                                  style: TextStyle(
                                    color: widget.color,
                                    fontSize: widget.size * 0.5,
                                    fontFamily: fontProvider.selectedFont,
                                    shadows: NeonColors.textGlow(widget.color, intensity: _glowAnimation.value),
                                  ),
                                ),
                                Text(
                                  weatherProvider.weather!.location,
                                  style: TextStyle(
                                    color: widget.color.withAlpha(179),
                                    fontSize: widget.size * 0.3,
                                    fontFamily: fontProvider.selectedFont,
                                    shadows: NeonColors.textGlow(widget.color, intensity: _glowAnimation.value * 0.5),
                                  ),
                                ),
                              ],
                            ),
                          ],
                        ),
                        const SizedBox(height: 8),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                          children: [
                            _buildWeatherDetail(
                              'Humidity',
                              weatherProvider.weather!.humidity,
                              widget.size,
                              fontProvider.selectedFont,
                            ),
                            _buildWeatherDetail(
                              'Wind',
                              weatherProvider.weather!.windSpeed,
                              widget.size,
                              fontProvider.selectedFont,
                            ),
                            _buildWeatherDetail(
                              'Feels Like',
                              weatherProvider.weather!.feelsLike,
                              widget.size,
                              fontProvider.selectedFont,
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
              ],
            ],
          ),
        );
      },
    );
  }

  String _formatHour(DateTime time, bool is24HourFormat) {
    if (is24HourFormat) {
      return time.hour.toString().padLeft(2, '0');
    } else {
      final hour = time.hour % 12;
      return (hour == 0 ? 12 : hour).toString().padLeft(2, '0');
    }
  }

  Widget _buildDigit(String digit, String fontFamily) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 2),
      child: Text(
        digit,
        style: TextStyle(
          color: widget.color,
          fontSize: widget.size,
          fontFamily: fontFamily,
          shadows: NeonColors.textGlow(widget.color, intensity: _glowAnimation.value),
        ),
      ),
    );
  }

  Widget _buildColon(String fontFamily) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 2),
      child: Text(
        ':',
        style: TextStyle(
          color: widget.color,
          fontSize: widget.size,
          fontFamily: fontFamily,
          shadows: NeonColors.textGlow(widget.color, intensity: _glowAnimation.value),
        ),
      ),
    );
  }

  String _formatDate(DateTime time) {
    final day = time.day.toString().padLeft(2, '0');
    final month = time.month.toString().padLeft(2, '0');
    final year = time.year.toString();

    return '$day/$month/$year';
  }

  IconData _getWeatherIcon(String condition) {
    switch (condition.toLowerCase()) {
      case 'clear':
        return WeatherIcons.day_sunny;
      case 'clouds':
        return WeatherIcons.cloudy;
      case 'rain':
        return WeatherIcons.rain;
      case 'snow':
        return WeatherIcons.snow;
      case 'thunderstorm':
        return WeatherIcons.thunderstorm;
      case 'drizzle':
        return WeatherIcons.sprinkle;
      case 'mist':
      case 'fog':
        return WeatherIcons.fog;
      default:
        return WeatherIcons.day_sunny;
    }
  }

  Widget _buildWeatherDetail(String label, String value, double size, String fontFamily) {
    return Column(
      children: [
        Text(
          label,
          style: TextStyle(
            color: widget.color.withAlpha(179),
            fontSize: size * 0.25,
            fontFamily: fontFamily,
            shadows: NeonColors.textGlow(widget.color, intensity: _glowAnimation.value * 0.3),
          ),
        ),
        Text(
          value,
          style: TextStyle(
            color: widget.color,
            fontSize: size * 0.3,
            fontFamily: fontFamily,
            shadows: NeonColors.textGlow(widget.color, intensity: _glowAnimation.value * 0.5),
          ),
        ),
      ],
    );
  }
}