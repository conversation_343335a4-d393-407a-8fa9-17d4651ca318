import 'dart:async';
import 'dart:math' as math;
import 'package:flutter/material.dart';

class SimpleFlipClock extends StatefulWidget {
  final bool showSeconds;
  final bool is24HourFormat;
  final Color digitColor;
  final Color backgroundColor;
  final double digitWidth;
  final double digitHeight;

  const SimpleFlipClock({
    Key? key,
    this.showSeconds = true,
    this.is24HourFormat = false,
    this.digitColor = Colors.white,
    this.backgroundColor = const Color(0xFF1C1B1F),
    this.digitWidth = 60,
    this.digitHeight = 90,
  }) : super(key: key);

  @override
  State<SimpleFlipClock> createState() => _SimpleFlipClockState();
}

class _SimpleFlipClockState extends State<SimpleFlipClock> with TickerProviderStateMixin {
  late Timer _timer;
  late DateTime _currentTime;
  
  // Animation controllers for each digit
  late AnimationController _hourTensController;
  late AnimationController _hourOnesController;
  late AnimationController _minuteTensController;
  late AnimationController _minuteOnesController;
  late AnimationController _secondTensController;
  late AnimationController _secondOnesController;
  late AnimationController _periodController;
  
  // Current and previous values
  late String _hourTens = '0';
  late String _hourOnes = '0';
  late String _minuteTens = '0';
  late String _minuteOnes = '0';
  late String _secondTens = '0';
  late String _secondOnes = '0';
  late String _period = 'AM';
  
  late String _prevHourTens = '0';
  late String _prevHourOnes = '0';
  late String _prevMinuteTens = '0';
  late String _prevMinuteOnes = '0';
  late String _prevSecondTens = '0';
  late String _prevSecondOnes = '0';
  late String _prevPeriod = 'AM';

  @override
  void initState() {
    super.initState();
    
    // Initialize animation controllers
    _hourTensController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 500),
    );
    _hourOnesController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 500),
    );
    _minuteTensController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 500),
    );
    _minuteOnesController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 500),
    );
    _secondTensController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 500),
    );
    _secondOnesController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 500),
    );
    _periodController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 500),
    );
    
    _updateTime();
    
    // Update time every second
    _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      _updateTime();
    });
  }

  @override
  void dispose() {
    _timer.cancel();
    _hourTensController.dispose();
    _hourOnesController.dispose();
    _minuteTensController.dispose();
    _minuteOnesController.dispose();
    _secondTensController.dispose();
    _secondOnesController.dispose();
    _periodController.dispose();
    super.dispose();
  }

  void _updateTime() {
    // Store previous values
    _prevHourTens = _hourTens;
    _prevHourOnes = _hourOnes;
    _prevMinuteTens = _minuteTens;
    _prevMinuteOnes = _minuteOnes;
    _prevSecondTens = _secondTens;
    _prevSecondOnes = _secondOnes;
    _prevPeriod = _period;
    
    // Get current time
    _currentTime = DateTime.now();
    
    // Format hour based on 12/24 hour setting
    int hour = _currentTime.hour;
    if (!widget.is24HourFormat) {
      _period = hour >= 12 ? 'PM' : 'AM';
      hour = hour % 12;
      if (hour == 0) hour = 12;
    }
    
    // Split digits
    final hourStr = hour.toString().padLeft(2, '0');
    final minuteStr = _currentTime.minute.toString().padLeft(2, '0');
    final secondStr = _currentTime.second.toString().padLeft(2, '0');
    
    _hourTens = hourStr[0];
    _hourOnes = hourStr[1];
    _minuteTens = minuteStr[0];
    _minuteOnes = minuteStr[1];
    _secondTens = secondStr[0];
    _secondOnes = secondStr[1];
    
    // Animate changing digits
    if (_hourTens != _prevHourTens) {
      _hourTensController.reset();
      _hourTensController.forward();
    }
    
    if (_hourOnes != _prevHourOnes) {
      _hourOnesController.reset();
      _hourOnesController.forward();
    }
    
    if (_minuteTens != _prevMinuteTens) {
      _minuteTensController.reset();
      _minuteTensController.forward();
    }
    
    if (_minuteOnes != _prevMinuteOnes) {
      _minuteOnesController.reset();
      _minuteOnesController.forward();
    }
    
    if (widget.showSeconds) {
      if (_secondTens != _prevSecondTens) {
        _secondTensController.reset();
        _secondTensController.forward();
      }
      
      if (_secondOnes != _prevSecondOnes) {
        _secondOnesController.reset();
        _secondOnesController.forward();
      }
    }
    
    if (_period != _prevPeriod) {
      _periodController.reset();
      _periodController.forward();
    }
  }

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        // Hours
        _buildDigit(_hourTens, _prevHourTens, _hourTensController),
        _buildDigit(_hourOnes, _prevHourOnes, _hourOnesController),
        
        // Separator
        _buildSeparator(),
        
        // Minutes
        _buildDigit(_minuteTens, _prevMinuteTens, _minuteTensController),
        _buildDigit(_minuteOnes, _prevMinuteOnes, _minuteOnesController),
        
        // Seconds (optional)
        if (widget.showSeconds) ...[
          _buildSeparator(),
          _buildDigit(_secondTens, _prevSecondTens, _secondTensController),
          _buildDigit(_secondOnes, _prevSecondOnes, _secondOnesController),
        ],
        
        // AM/PM indicator (for 12-hour format)
        if (!widget.is24HourFormat) ...[
          const SizedBox(width: 16),
          _buildPeriod(),
        ],
      ],
    );
  }

  Widget _buildDigit(String current, String previous, AnimationController controller) {
    return Container(
      width: widget.digitWidth,
      height: widget.digitHeight,
      margin: const EdgeInsets.symmetric(horizontal: 2),
      child: AnimatedBuilder(
        animation: controller,
        builder: (context, child) {
          return _buildFlipPanel(
            current: current,
            previous: previous,
            animation: controller.value,
          );
        },
      ),
    );
  }

  Widget _buildFlipPanel({
    required String current,
    required String previous,
    required double animation,
  }) {
    final isFlipping = animation > 0 && animation < 1;
    final isHalfFlipped = animation >= 0.5;
    
    return Container(
      decoration: BoxDecoration(
        color: widget.backgroundColor,
        borderRadius: BorderRadius.circular(8),
        boxShadow: const [
          BoxShadow(
            color: Color(0x4D000000), // 0.3 opacity black
            blurRadius: 4,
            offset: Offset(0, 2),
          ),
        ],
      ),
      child: Stack(
        children: [
          // Bottom half (always shows current digit)
          Align(
            alignment: Alignment.bottomCenter,
            child: ClipRect(
              child: Align(
                alignment: Alignment.bottomCenter,
                heightFactor: 0.5,
                child: Container(
                  width: widget.digitWidth,
                  height: widget.digitHeight,
                  color: widget.backgroundColor,
                  alignment: Alignment.center,
                  child: Text(
                    current,
                    style: TextStyle(
                      fontSize: widget.digitHeight * 0.6,
                      fontWeight: FontWeight.bold,
                      color: widget.digitColor,
                    ),
                  ),
                ),
              ),
            ),
          ),
          
          // Top half (shows previous digit when not flipping)
          if (!isFlipping)
            Align(
              alignment: Alignment.topCenter,
              child: ClipRect(
                child: Align(
                  alignment: Alignment.topCenter,
                  heightFactor: 0.5,
                  child: Container(
                    width: widget.digitWidth,
                    height: widget.digitHeight,
                    color: widget.backgroundColor,
                    alignment: Alignment.center,
                    child: Text(
                      current,
                      style: TextStyle(
                        fontSize: widget.digitHeight * 0.6,
                        fontWeight: FontWeight.bold,
                        color: widget.digitColor,
                      ),
                    ),
                  ),
                ),
              ),
            ),
            
          // Flipping top half
          if (isFlipping)
            Align(
              alignment: Alignment.topCenter,
              child: ClipRect(
                child: Align(
                  alignment: Alignment.topCenter,
                  heightFactor: 0.5,
                  child: Transform(
                    alignment: Alignment.bottomCenter,
                    transform: Matrix4.identity()
                      ..setEntry(3, 2, 0.001)
                      ..rotateX(animation * math.pi),
                    child: Container(
                      width: widget.digitWidth,
                      height: widget.digitHeight,
                      color: widget.backgroundColor,
                      alignment: Alignment.center,
                      child: Text(
                        isHalfFlipped ? current : previous,
                        style: TextStyle(
                          fontSize: widget.digitHeight * 0.6,
                          fontWeight: FontWeight.bold,
                          color: widget.digitColor,
                        ),
                      ),
                    ),
                  ),
                ),
              ),
            ),
            
          // Dividing line
          Align(
            alignment: Alignment.center,
            child: Container(
              width: widget.digitWidth,
              height: 1,
              color: Colors.black26,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSeparator() {
    return SizedBox(
      width: 20,
      child: Center(
        child: Text(
          ':',
          style: TextStyle(
            fontSize: widget.digitHeight * 0.6,
            fontWeight: FontWeight.bold,
            color: widget.digitColor,
          ),
        ),
      ),
    );
  }

  Widget _buildPeriod() {
    return AnimatedBuilder(
      animation: _periodController,
      builder: (context, child) {
        return Container(
          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
          decoration: BoxDecoration(
            color: widget.backgroundColor,
            borderRadius: BorderRadius.circular(8),
            boxShadow: const [
              BoxShadow(
                color: Color(0x4D000000), // 0.3 opacity black
                blurRadius: 4,
                offset: Offset(0, 2),
              ),
            ],
          ),
          child: Text(
            _period,
            style: TextStyle(
              fontSize: widget.digitHeight * 0.3,
              fontWeight: FontWeight.bold,
              color: widget.digitColor,
            ),
          ),
        );
      },
    );
  }
}
