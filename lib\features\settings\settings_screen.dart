import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../providers/theme_provider.dart';
import '../../providers/font_provider.dart';
import '../../providers/color_provider.dart';
import '../../core/theme/neon_colors.dart';
import '../../core/widgets/neon_container.dart';

class SettingsScreen extends StatelessWidget {
  const SettingsScreen({super.key});

  // Helper method to build a color category section
  Widget _buildColorCategory(String title, List<Color> colors, ColorProvider colorProvider) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.only(top: 16, bottom: 8),
          child: Text(
            title,
            style: TextStyle(
              color: colorProvider.currentColor,
              fontSize: 14,
              fontWeight: FontWeight.bold,
              shadows: NeonColors.textGlow(
                colorProvider.currentColor,
                intensity: 0.3,
              ),
            ),
          ),
        ),
        Wrap(
          spacing: 8,
          runSpacing: 8,
          children: colors.map((color) => GestureDetector(
            onTap: () {
              colorProvider.setStaticColor(color);
            },
            child: Container(
              width: 32,
              height: 32,
              decoration: BoxDecoration(
                color: color,
                shape: BoxShape.circle,
                border: Border.all(
                  color: colorProvider.currentColor == color
                      ? Colors.white
                      : color,
                  width: 2,
                ),
                boxShadow: colorProvider.currentColor == color
                    ? NeonColors.neonGlow(color, intensity: 0.5)
                    : null,
              ),
            ),
          )).toList(),
        ),
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    final themeProvider = Provider.of<ThemeProvider>(context);
    final fontProvider = Provider.of<FontProvider>(context);
    final colorProvider = Provider.of<ColorProvider>(context);

    return Container(
      decoration: const BoxDecoration(
        gradient: NeonColors.cyberGradient,
      ),
      child: Scaffold(
        backgroundColor: Colors.transparent,
        appBar: AppBar(
          backgroundColor: Colors.transparent,
          title: Text(
            'Settings',
            style: TextStyle(
              color: colorProvider.currentColor,
              shadows: NeonColors.textGlow(
                colorProvider.currentColor,
                intensity: 0.5,
              ),
            ),
          ),
          leading: IconButton(
            icon: Icon(
              Icons.arrow_back,
              color: colorProvider.currentColor,
            ),
            onPressed: () => Navigator.pop(context),
          ),
        ),
        body: ListView(
          padding: const EdgeInsets.all(16.0),
          children: [
            // Display Settings
            NeonContainer(
              borderColor: colorProvider.currentColor,
              glowIntensity: 0.5,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: Text(
                      'Display Settings',
                      style: TextStyle(
                        color: colorProvider.currentColor,
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        shadows: NeonColors.textGlow(
                          colorProvider.currentColor,
                          intensity: 0.5,
                        ),
                      ),
                    ),
                  ),
                  SwitchListTile(
                    title: Text(
                      'Show Seconds',
                      style: TextStyle(
                        color: colorProvider.currentColor,
                        shadows: NeonColors.textGlow(
                          colorProvider.currentColor,
                          intensity: 0.3,
                        ),
                      ),
                    ),
                    value: themeProvider.showSeconds,
                    onChanged: (value) {
                      themeProvider.setShowSeconds(value);
                    },
                    activeColor: colorProvider.currentColor,
                  ),
                  SwitchListTile(
                    title: Text(
                      'Show Date',
                      style: TextStyle(
                        color: colorProvider.currentColor,
                        shadows: NeonColors.textGlow(
                          colorProvider.currentColor,
                          intensity: 0.3,
                        ),
                      ),
                    ),
                    value: themeProvider.showDate,
                    onChanged: (value) {
                      themeProvider.setShowDate(value);
                    },
                    activeColor: colorProvider.currentColor,
                  ),
                  SwitchListTile(
                    title: Text(
                      'Show Weather',
                      style: TextStyle(
                        color: colorProvider.currentColor,
                        shadows: NeonColors.textGlow(
                          colorProvider.currentColor,
                          intensity: 0.3,
                        ),
                      ),
                    ),
                    value: themeProvider.showWeather,
                    onChanged: (value) {
                      themeProvider.setShowWeather(value);
                    },
                    activeColor: colorProvider.currentColor,
                  ),
                  SwitchListTile(
                    title: Text(
                      '24-Hour Format',
                      style: TextStyle(
                        color: colorProvider.currentColor,
                        shadows: NeonColors.textGlow(
                          colorProvider.currentColor,
                          intensity: 0.3,
                        ),
                      ),
                    ),
                    value: themeProvider.is24HourFormat,
                    onChanged: (value) {
                      themeProvider.set24HourFormat(value);
                    },
                    activeColor: colorProvider.currentColor,
                  ),
                ],
              ),
            ),
            const SizedBox(height: 16),
            // Color Settings
            NeonContainer(
              borderColor: colorProvider.currentColor,
              glowIntensity: 0.5,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: Text(
                      'Color Settings',
                      style: TextStyle(
                        color: colorProvider.currentColor,
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        shadows: NeonColors.textGlow(
                          colorProvider.currentColor,
                          intensity: 0.5,
                        ),
                      ),
                    ),
                  ),
                  SwitchListTile(
                    title: Text(
                      'Rainbow Mode',
                      style: TextStyle(
                        color: colorProvider.currentColor,
                        shadows: NeonColors.textGlow(
                          colorProvider.currentColor,
                          intensity: 0.3,
                        ),
                      ),
                    ),
                    value: colorProvider.isRainbowMode,
                    onChanged: (value) {
                      colorProvider.toggleRainbowMode();
                    },
                    activeColor: colorProvider.currentColor,
                  ),
                  if (!colorProvider.isRainbowMode)
                    Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'Static Color',
                            style: TextStyle(
                              color: colorProvider.currentColor,
                              shadows: NeonColors.textGlow(
                                colorProvider.currentColor,
                                intensity: 0.3,
                              ),
                            ),
                          ),
                          const SizedBox(height: 8),
                          Wrap(
                            spacing: 8,
                            runSpacing: 8,
                            children: [
                              // Classic Neon Colors
                              _buildColorCategory(
                                'Classic Neon',
                                [
                                  NeonColors.neonBlue,
                                  NeonColors.neonPink,
                                  NeonColors.neonPurple,
                                  NeonColors.neonGreen,
                                  NeonColors.neonYellow,
                                  NeonColors.neonRed,
                                  NeonColors.neonCyan,
                                  NeonColors.neonOrange,
                                  NeonColors.neonTeal,
                                  NeonColors.neonLime,
                                ],
                                colorProvider,
                              ),

                              // Vaporwave Colors
                              _buildColorCategory(
                                'Vaporwave',
                                [
                                  NeonColors.vaporwavePink,
                                  NeonColors.vaporwaveBlue,
                                ],
                                colorProvider,
                              ),

                              // Retrowave Colors
                              _buildColorCategory(
                                'Retrowave',
                                [
                                  NeonColors.retroWaveRed,
                                  NeonColors.retroWavePurple,
                                ],
                                colorProvider,
                              ),

                              // Pastel Colors
                              _buildColorCategory(
                                'Pastel',
                                [
                                  NeonColors.pastelLavender,
                                  NeonColors.pastelPink,
                                  NeonColors.pastelBlue,
                                  NeonColors.pastelYellow,
                                  NeonColors.mintGreen,
                                ],
                                colorProvider,
                              ),

                              // Modern Colors
                              _buildColorCategory(
                                'Modern',
                                [
                                  NeonColors.modernGold,
                                  NeonColors.modernSilver,
                                  NeonColors.modernRose,
                                  NeonColors.modernSky,
                                  NeonColors.modernMint,
                                ],
                                colorProvider,
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                  if (colorProvider.isRainbowMode)
                    Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'Rainbow Speed',
                            style: TextStyle(
                              color: colorProvider.currentColor,
                              shadows: NeonColors.textGlow(
                                colorProvider.currentColor,
                                intensity: 0.3,
                              ),
                            ),
                          ),
                          Slider(
                            value: colorProvider.colorSpeed,
                            min: 0.1,
                            max: 2.0,
                            divisions: 19,
                            label: colorProvider.colorSpeed.toStringAsFixed(1),
                            onChanged: (value) {
                              colorProvider.setColorSpeed(value);
                            },
                            activeColor: colorProvider.currentColor,
                          ),
                        ],
                      ),
                    ),
                ],
              ),
            ),
            const SizedBox(height: 16),
            // Theme Settings
            NeonContainer(
              borderColor: colorProvider.currentColor,
              glowIntensity: 0.5,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: Text(
                      'Theme Settings',
                      style: TextStyle(
                        color: colorProvider.currentColor,
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        shadows: NeonColors.textGlow(
                          colorProvider.currentColor,
                          intensity: 0.5,
                        ),
                      ),
                    ),
                  ),
                  Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 16.0),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Select Theme',
                          style: TextStyle(
                            color: colorProvider.currentColor,
                            shadows: NeonColors.textGlow(
                              colorProvider.currentColor,
                              intensity: 0.3,
                            ),
                          ),
                        ),
                        const SizedBox(height: 8),
                        Container(
                          decoration: BoxDecoration(
                            color: NeonColors.darkCyber,
                            borderRadius: BorderRadius.circular(8),
                            border: Border.all(
                              color: colorProvider.currentColor,
                              width: 1,
                            ),
                          ),
                          child: DropdownButtonHideUnderline(
                            child: DropdownButton<String>(
                              value: themeProvider.selectedTheme,
                              isExpanded: true,
                              dropdownColor: NeonColors.darkCyber,
                              padding: const EdgeInsets.symmetric(horizontal: 16),
                              style: TextStyle(
                                color: colorProvider.currentColor,
                                fontSize: 16,
                                shadows: NeonColors.textGlow(
                                  colorProvider.currentColor,
                                  intensity: 0.3,
                                ),
                              ),
                              items: themeProvider.availableThemes.map((String theme) {
                                return DropdownMenuItem<String>(
                                  value: theme,
                                  child: Text(theme),
                                );
                              }).toList(),
                              onChanged: (String? newValue) {
                                if (newValue != null) {
                                  themeProvider.setTheme(newValue);
                                }
                              },
                            ),
                          ),
                        ),
                        const SizedBox(height: 16),
                        // Theme preview
                        Container(
                          height: 100,
                          width: double.infinity,
                          decoration: BoxDecoration(
                            gradient: themeProvider.backgroundGradient,
                            borderRadius: BorderRadius.circular(8),
                            border: Border.all(
                              color: colorProvider.currentColor,
                              width: 1,
                            ),
                          ),
                          child: Center(
                            child: Text(
                              'Theme Preview',
                              style: TextStyle(
                                color: themeProvider.textColor,
                                fontSize: 18,
                                fontWeight: FontWeight.bold,
                                shadows: NeonColors.textGlow(
                                  colorProvider.currentColor,
                                  intensity: 0.5,
                                ),
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                  const SizedBox(height: 16),
                ],
              ),
            ),
            const SizedBox(height: 16),
            // Font Settings
            NeonContainer(
              borderColor: colorProvider.currentColor,
              glowIntensity: 0.5,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: Text(
                      'Font Settings',
                      style: TextStyle(
                        color: colorProvider.currentColor,
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        shadows: NeonColors.textGlow(
                          colorProvider.currentColor,
                          intensity: 0.5,
                        ),
                      ),
                    ),
                  ),
                  Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 16.0),
                    child: DropdownButton<String>(
                      value: fontProvider.selectedFont,
                      isExpanded: true,
                      dropdownColor: NeonColors.darkCyber,
                      style: TextStyle(
                        color: colorProvider.currentColor,
                        fontSize: 16,
                        shadows: NeonColors.textGlow(
                          colorProvider.currentColor,
                          intensity: 0.3,
                        ),
                      ),
                      items: fontProvider.availableFonts.map((String font) {
                        return DropdownMenuItem<String>(
                          value: font,
                          child: Text(font),
                        );
                      }).toList(),
                      onChanged: (String? newValue) {
                        if (newValue != null) {
                          fontProvider.selectFont(newValue);
                        }
                      },
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 16),
            // Glow Settings
            NeonContainer(
              borderColor: colorProvider.currentColor,
              glowIntensity: 0.5,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: Text(
                      'Glow Settings',
                      style: TextStyle(
                        color: colorProvider.currentColor,
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        shadows: NeonColors.textGlow(
                          colorProvider.currentColor,
                          intensity: 0.5,
                        ),
                      ),
                    ),
                  ),
                  Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 16.0),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Glow Intensity',
                          style: TextStyle(
                            color: colorProvider.currentColor,
                            shadows: NeonColors.textGlow(
                              colorProvider.currentColor,
                              intensity: 0.3,
                            ),
                          ),
                        ),
                        Slider(
                          value: themeProvider.glowIntensity,
                          min: 0.1,
                          max: 1.0,
                          divisions: 9,
                          label: themeProvider.glowIntensity.toStringAsFixed(1),
                          onChanged: (value) {
                            themeProvider.setGlowIntensity(value);
                          },
                          activeColor: colorProvider.currentColor,
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 16),
            // Weather Settings
            NeonContainer(
              borderColor: colorProvider.currentColor,
              glowIntensity: 0.5,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: Text(
                      'Weather Settings',
                      style: TextStyle(
                        color: colorProvider.currentColor,
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        shadows: NeonColors.textGlow(
                          colorProvider.currentColor,
                          intensity: 0.5,
                        ),
                      ),
                    ),
                  ),
                  SwitchListTile(
                    title: Text(
                      'Use Manual Location',
                      style: TextStyle(
                        color: colorProvider.currentColor,
                        shadows: NeonColors.textGlow(
                          colorProvider.currentColor,
                          intensity: 0.3,
                        ),
                      ),
                    ),
                    value: themeProvider.useManualLocation,
                    onChanged: (value) {
                      themeProvider.setUseManualLocation(value);
                    },
                    activeColor: colorProvider.currentColor,
                  ),
                  if (themeProvider.useManualLocation)
                    Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 16.0),
                      child: TextField(
                        controller: TextEditingController(text: themeProvider.manualLocation),
                        decoration: const InputDecoration(
                          labelText: 'Enter Location',
                          hintText: 'e.g., London, UK',
                          border: OutlineInputBorder(),
                        ),
                        onChanged: (value) => themeProvider.setManualLocation(value),
                      ),
                    ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}