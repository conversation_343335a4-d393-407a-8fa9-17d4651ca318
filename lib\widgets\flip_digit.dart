import 'dart:math' as math;
import 'package:flutter/material.dart';

/// A widget that displays a flipping digit animation like in mechanical flip clocks
class FlipDigit extends StatefulWidget {
  final String currentValue;
  final String nextValue;
  final double height;
  final double width;
  final Color digitColor;
  final Color backgroundColor;
  final bool isFlipping;
  final VoidCallback? onFlipCompleted;

  const FlipDigit({
    Key? key,
    required this.currentValue,
    required this.nextValue,
    required this.height,
    required this.width,
    required this.digitColor,
    required this.backgroundColor,
    this.isFlipping = false,
    this.onFlipCompleted,
  }) : super(key: key);

  @override
  State<FlipDigit> createState() => _FlipDigitState();
}

class _FlipDigitState extends State<FlipDigit> with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _animation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 450),
    );

    _animation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(
        parent: _controller,
        curve: Curves.easeInOut,
      ),
    );

    _animation.addStatusListener((status) {
      if (status == AnimationStatus.completed) {
        if (widget.onFlipCompleted != null) {
          widget.onFlipCompleted!();
        }
      }
    });

    if (widget.isFlipping) {
      _controller.forward();
    }
  }

  @override
  void didUpdateWidget(FlipDigit oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.isFlipping && !oldWidget.isFlipping) {
      _controller.reset();
      _controller.forward();
    }
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _animation,
      builder: (context, child) {
        return Container(
          width: widget.width,
          height: widget.height,
          decoration: BoxDecoration(
            color: widget.backgroundColor,
            borderRadius: BorderRadius.circular(8),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withAlpha(77),
                blurRadius: 5,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Stack(
            children: [
              // Top half (static)
              ClipRect(
                child: Align(
                  alignment: Alignment.topCenter,
                  heightFactor: 0.5,
                  child: Container(
                    width: widget.width,
                    height: widget.height,
                    decoration: BoxDecoration(
                      color: widget.backgroundColor,
                      borderRadius: const BorderRadius.vertical(top: Radius.circular(8)),
                      gradient: LinearGradient(
                        begin: Alignment.topCenter,
                        end: Alignment.bottomCenter,
                        colors: [
                          widget.backgroundColor,
                          widget.backgroundColor.withAlpha(230),
                        ],
                      ),
                    ),
                    child: Center(
                      child: Text(
                        _animation.value < 0.5 ? widget.currentValue : widget.nextValue,
                        style: TextStyle(
                          fontSize: widget.height * 0.7,
                          fontWeight: FontWeight.bold,
                          color: widget.digitColor,
                        ),
                      ),
                    ),
                  ),
                ),
              ),

              // Bottom half (static)
              Positioned(
                top: widget.height / 2,
                left: 0,
                right: 0,
                bottom: 0,
                child: ClipRect(
                  child: Align(
                    alignment: Alignment.bottomCenter,
                    heightFactor: 0.5,
                    child: Container(
                      width: widget.width,
                      height: widget.height,
                      decoration: BoxDecoration(
                        color: widget.backgroundColor,
                        borderRadius: const BorderRadius.vertical(bottom: Radius.circular(8)),
                        gradient: LinearGradient(
                          begin: Alignment.topCenter,
                          end: Alignment.bottomCenter,
                          colors: [
                            widget.backgroundColor.withAlpha(230),
                            widget.backgroundColor,
                          ],
                        ),
                      ),
                      child: Center(
                        child: Text(
                          _animation.value >= 0.5 ? widget.nextValue : widget.currentValue,
                          style: TextStyle(
                            fontSize: widget.height * 0.7,
                            fontWeight: FontWeight.bold,
                            color: widget.digitColor,
                          ),
                        ),
                      ),
                    ),
                  ),
                ),
              ),

              // Flipping top half
              Positioned(
                top: 0,
                left: 0,
                right: 0,
                height: widget.height / 2,
                child: AnimatedOpacity(
                  opacity: _animation.value < 0.5 ? 1.0 : 0.0,
                  duration: const Duration(milliseconds: 200),
                  child: Transform(
                    alignment: Alignment.bottomCenter,
                    transform: Matrix4.identity()
                      ..setEntry(3, 2, 0.001)
                      ..rotateX(_animation.value * math.pi),
                    child: Container(
                      decoration: BoxDecoration(
                        color: widget.backgroundColor,
                        borderRadius: const BorderRadius.vertical(top: Radius.circular(8)),
                        gradient: LinearGradient(
                          begin: Alignment.topCenter,
                          end: Alignment.bottomCenter,
                          colors: [
                            widget.backgroundColor,
                            widget.backgroundColor.withAlpha(230),
                          ],
                        ),
                      ),
                      child: Center(
                        child: ClipRect(
                          child: Align(
                            alignment: Alignment.topCenter,
                            heightFactor: 0.5,
                            child: Text(
                              widget.currentValue,
                              style: TextStyle(
                                fontSize: widget.height * 0.7,
                                fontWeight: FontWeight.bold,
                                color: widget.digitColor,
                              ),
                            ),
                          ),
                        ),
                      ),
                    ),
                  ),
                ),
              ),

              // Flipping bottom half
              Positioned(
                top: widget.height / 2,
                left: 0,
                right: 0,
                height: widget.height / 2,
                child: AnimatedOpacity(
                  opacity: _animation.value >= 0.5 ? 1.0 : 0.0,
                  duration: const Duration(milliseconds: 200),
                  child: Transform(
                    alignment: Alignment.topCenter,
                    transform: Matrix4.identity()
                      ..setEntry(3, 2, 0.001)
                      ..rotateX((_animation.value - 1) * math.pi),
                    child: Container(
                      decoration: BoxDecoration(
                        color: widget.backgroundColor,
                        borderRadius: const BorderRadius.vertical(bottom: Radius.circular(8)),
                        gradient: LinearGradient(
                          begin: Alignment.topCenter,
                          end: Alignment.bottomCenter,
                          colors: [
                            widget.backgroundColor.withAlpha(230),
                            widget.backgroundColor,
                          ],
                        ),
                      ),
                      child: Center(
                        child: ClipRect(
                          child: Align(
                            alignment: Alignment.bottomCenter,
                            heightFactor: 0.5,
                            child: Text(
                              widget.nextValue,
                              style: TextStyle(
                                fontSize: widget.height * 0.7,
                                fontWeight: FontWeight.bold,
                                color: widget.digitColor,
                              ),
                            ),
                          ),
                        ),
                      ),
                    ),
                  ),
                ),
              ),

              // Dividing line
              Positioned(
                top: widget.height / 2 - 0.5,
                left: 0,
                right: 0,
                height: 1,
                child: Container(
                  color: Colors.black.withAlpha(77),
                ),
              ),
            ],
          ),
        );
      },
    );
  }
}
