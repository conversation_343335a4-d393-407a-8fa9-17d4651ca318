import 'package:flutter/material.dart';
import '../constants/app_colors.dart';

extension ContextExtensions on BuildContext {
  // Screen size helpers
  double get screenWidth => MediaQuery.of(this).size.width;
  double get screenHeight => MediaQuery.of(this).size.height;
  bool get isSmallScreen => screenWidth < 600;
  bool get isMediumScreen => screenWidth >= 600 && screenWidth < 1200;
  bool get isLargeScreen => screenWidth >= 1200;

  // Navigation helpers
  void pop() => Navigator.of(this).pop();
  void popUntil(String routeName) => Navigator.of(this).popUntil((route) => route.settings.name == routeName);
  Future<T?> push<T>(Widget page) => Navigator.of(this).push(MaterialPageRoute(builder: (_) => page));
  Future<T?> pushReplacement<T>(Widget page) => Navigator.of(this).pushReplacement(MaterialPageRoute(builder: (_) => page));
  Future<T?> pushNamed<T>(String routeName, {Object? arguments}) => Navigator.of(this).pushNamed(routeName, arguments: arguments);

  // Snackbar helpers
  void showSnackBar(String message, {bool isError = false}) {
    ScaffoldMessenger.of(this).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: isError ? AppColors.error : AppColors.primary,
        behavior: SnackBarBehavior.floating,
      ),
    );
  }
} 