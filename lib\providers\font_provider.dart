import 'package:flutter/material.dart';
import '../providers/theme_provider.dart';
import '../providers/clock_style_provider.dart';

class FontProvider extends ChangeNotifier {
  // Selected font
  String _selectedFont = 'Digital7';

  // Current font for theme/clock style combination
  String _currentFont = 'Roboto';

  // Available digital clock fonts
  final List<String> _availableFonts = [
    'Digital7',
    'DigitalMono',
    'DigitalReadout',
    'DigitalReadoutExp',
    'DigitalReadoutUpright',
    'Roboto',
    'Orbitron',
    'Audiowide',
    'Quicksand',
    'VCR_OSD_Mono',
    'Montserrat',
    'Comfortaa',
  ];

  // Theme-specific fonts
  final Map<String, String> _themeFonts = {
    'Dark': 'Roboto',
    'Light': 'Roboto',
    'Amoled': 'Orbitron',
    'Neon': 'Audiowide',
    'Minimal': 'Quicksand',
    'Vaporwave': 'VCR_OSD_Mono',
    'Retrowave': 'Orbitron',
    'Pastel': 'Comfortaa',
    'Modern Minimal': 'Montser<PERSON>',
    'Sunrise': 'Quicksand',
    'Night Sky': 'Orbitron',
  };

  // Clock style specific fonts
  final Map<ClockStyle, String> _clockStyleFonts = {
    ClockStyle.digital: 'Digital7',
    ClockStyle.digitalWithSeconds: 'Digital7',
    ClockStyle.analogBasic: 'Roboto',
    ClockStyle.analogModern: 'Montserrat',
    ClockStyle.analogMinimal: 'Quicksand',
    ClockStyle.binaryClock: 'Orbitron',
  };

  // Getters
  String get selectedFont => _selectedFont;
  String get currentFont => _currentFont;
  List<String> get availableFonts => _availableFonts;

  // Select a font for digital clock
  void selectFont(String font) {
    if (_availableFonts.contains(font)) {
      _selectedFont = font;
      _currentFont = font; // Update current font immediately
      notifyListeners();
    }
  }

  // Get font for a specific theme
  String getFontForTheme(String theme) {
    return _themeFonts[theme] ?? 'Roboto';
  }

  // Get font for a specific clock style
  String getFontForClockStyle(ClockStyle style) {
    return _clockStyleFonts[style] ?? 'Roboto';
  }

  // Update font based on theme and clock style
  void updateFont(String theme, ClockStyle clockStyle) {
    // Prioritize user selection if available
    if (_availableFonts.contains(_selectedFont)) {
      _currentFont = _selectedFont;
    } else {
      // Fallback to clock style font or theme font
      _currentFont = _clockStyleFonts[clockStyle] ?? _themeFonts[theme] ?? 'Roboto';
    }
    notifyListeners();
  }

  // Get TextStyle for digital clock
  TextStyle getDigitalClockStyle({
    required ThemeProvider themeProvider,
    required double fontSize,
    FontWeight fontWeight = FontWeight.w300,
    Color? color,
  }) {
    return TextStyle(
      fontFamily: _currentFont,
      fontSize: fontSize,
      fontWeight: fontWeight,
      color: color ?? themeProvider.textColor,
    );
  }

  // Get TextStyle for date
  TextStyle getDateStyle({
    required ThemeProvider themeProvider,
    required double fontSize,
    FontWeight fontWeight = FontWeight.w400,
    double opacity = 0.8,
  }) {
    return TextStyle(
      fontFamily: _currentFont,
      fontSize: fontSize,
      fontWeight: fontWeight,
      color: themeProvider.textColor.withAlpha((opacity * 255).round()),
    );
  }

  // Get TextStyle for battery info
  TextStyle getBatteryStyle({
    required Color color,
    required double fontSize,
    FontWeight fontWeight = FontWeight.w500,
  }) {
    return TextStyle(
      fontFamily: _currentFont,
      fontSize: fontSize,
      fontWeight: fontWeight,
      color: color,
    );
  }
}