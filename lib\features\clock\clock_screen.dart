import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import 'package:battery_plus/battery_plus.dart';
import '../settings/settings_screen.dart';
import '../weather/weather_details_screen.dart';
import '../../core/theme/neon_colors.dart';
import '../../providers/theme_provider.dart';
import '../../providers/weather_provider.dart';
import '../../providers/font_provider.dart';
import '../../providers/color_provider.dart';
import '../../core/widgets/neon_container.dart';
import 'widgets/led_clock.dart';
import 'dart:async';

class ClockScreen extends StatefulWidget {
  const ClockScreen({super.key});

  @override
  State<ClockScreen> createState() => _ClockScreenState();
}

class _ClockScreenState extends State<ClockScreen> with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  DateTime _currentTime = DateTime.now();
  final Battery _battery = Battery();
  int _batteryLevel = 100;
  BatteryState _batteryState = BatteryState.full;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      vsync: this,
      duration: const Duration(seconds: 1),
    )..repeat();

    _controller.addListener(() {
      setState(() {
        _currentTime = DateTime.now();
      });
    });

    // Initialize providers after build
    WidgetsBinding.instance.addPostFrameCallback((_) {
      Provider.of<ColorProvider>(context, listen: false).initialize(this);
      _updateWeather();
      _initBattery();
    });
  }

  Future<void> _initBattery() async {
    _batteryLevel = await _battery.batteryLevel;
    _batteryState = await _battery.batteryState;
    setState(() {});

    _battery.onBatteryStateChanged.listen((BatteryState state) {
      setState(() {
        _batteryState = state;
      });
    });

    // Update battery level periodically
    Timer.periodic(const Duration(seconds: 30), (timer) async {
      final level = await _battery.batteryLevel;
      if (mounted) {
        setState(() {
          _batteryLevel = level;
        });
      }
    });
  }

  IconData _getBatteryIcon() {
    if (_batteryState == BatteryState.charging) {
      return Icons.battery_charging_full;
    }
    if (_batteryLevel >= 90) {
      return Icons.battery_full;
    } else if (_batteryLevel >= 60) {
      return Icons.battery_6_bar;
    } else if (_batteryLevel >= 30) {
      return Icons.battery_4_bar;
    } else if (_batteryLevel >= 10) {
      return Icons.battery_2_bar;
    } else {
      return Icons.battery_alert;
    }
  }

  void _updateWeather() {
    Provider.of<WeatherProvider>(context, listen: false).fetchWeather();
  }

  // Removed unused methods

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final themeProvider = Provider.of<ThemeProvider>(context);
    final fontProvider = Provider.of<FontProvider>(context);
    final colorProvider = Provider.of<ColorProvider>(context);
    final weatherProvider = Provider.of<WeatherProvider>(context);

    return Container(
      decoration: const BoxDecoration(
        gradient: NeonColors.cyberGradient,
      ),
      child: Scaffold(
        backgroundColor: Colors.transparent,
        extendBodyBehindAppBar: true,
        extendBody: true,
        appBar: AppBar(
          backgroundColor: Colors.transparent,
          elevation: 0,
          systemOverlayStyle: const SystemUiOverlayStyle(
            statusBarColor: Colors.transparent,
            statusBarIconBrightness: Brightness.light,
            systemNavigationBarColor: Colors.transparent,
            systemNavigationBarIconBrightness: Brightness.light,
          ),
          actions: [
            Padding(
              padding: const EdgeInsets.only(right: 16.0),
              child: Row(
                children: [
                  Icon(
                    _getBatteryIcon(),
                    color: colorProvider.currentColor,
                  ),
                  const SizedBox(width: 4),
                  Text(
                    '$_batteryLevel%',
                    style: TextStyle(
                      color: colorProvider.currentColor,
                      fontSize: 16,
                      shadows: NeonColors.textGlow(
                        colorProvider.currentColor,
                        intensity: themeProvider.glowIntensity,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
        body: Stack(
          children: [
            // Grid background
            CustomPaint(
              size: Size.infinite,
              painter: GridPainter(
                color: NeonColors.neonBlue.withAlpha(25), // 0.1 * 255 = 25
                spacing: 40,
              ),
            ),
            // Main content
            Column(
              children: [
                Expanded(
                  child: Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        // LED Clock
                        LEDClock(
                          time: _currentTime,
                          color: colorProvider.currentColor,
                          size: MediaQuery.of(context).size.width * 0.8,
                          showSeconds: themeProvider.showSeconds,
                          showDate: themeProvider.showDate,
                          showWeather: themeProvider.showWeather,
                          glowIntensity: themeProvider.glowIntensity,
                        ),

                        // Weather and date are now handled by the LEDClock widget
                      ],
                    ),
                  ),
                ),
                Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: NeonContainer(
                    borderColor: colorProvider.currentColor,
                    glowIntensity: 0.5,
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                      children: [
                        // Font selection dropdown
                        DropdownButton<String>(
                          value: fontProvider.selectedFont,
                          dropdownColor: NeonColors.darkCyber,
                          style: TextStyle(
                            color: colorProvider.currentColor,
                            fontSize: 16,
                            shadows: NeonColors.textGlow(
                              colorProvider.currentColor,
                              intensity: 0.5,
                            ),
                          ),
                          items: fontProvider.availableFonts.map((String font) {
                            return DropdownMenuItem<String>(
                              value: font,
                              child: Text(font),
                            );
                          }).toList(),
                          onChanged: (String? newValue) {
                            if (newValue != null) {
                              fontProvider.selectFont(newValue);
                            }
                          },
                        ),
                        // Weather button
                        IconButton(
                          icon: Icon(
                            Icons.cloud,
                            color: colorProvider.currentColor,
                          ),
                          onPressed: () {
                            final location = weatherProvider.weather?.location ?? 'Agra, India';
                            Navigator.push(
                              context,
                              MaterialPageRoute(
                                builder: (context) => WeatherDetailsScreen(
                                  location: location,
                                ),
                              ),
                            );
                          },
                        ),
                        // Settings button
                        IconButton(
                          icon: Icon(
                            Icons.settings,
                            color: colorProvider.currentColor,
                          ),
                          onPressed: () {
                            Navigator.push(
                              context,
                              MaterialPageRoute(
                                builder: (context) => const SettingsScreen(),
                              ),
                            );
                          },
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}

class GridPainter extends CustomPainter {
  final Color color;
  final double spacing;

  GridPainter({
    required this.color,
    required this.spacing,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = color
      ..strokeWidth = 1.0;

    // Draw vertical lines
    for (double x = 0; x < size.width; x += spacing) {
      canvas.drawLine(
        Offset(x, 0),
        Offset(x, size.height),
        paint,
      );
    }

    // Draw horizontal lines
    for (double y = 0; y < size.height; y += spacing) {
      canvas.drawLine(
        Offset(0, y),
        Offset(size.width, y),
        paint,
      );
    }
  }

  @override
  bool shouldRepaint(GridPainter oldDelegate) =>
      color != oldDelegate.color || spacing != oldDelegate.spacing;
}