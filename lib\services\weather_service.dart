import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:geolocator/geolocator.dart';
import 'package:smart_clock/providers/theme_provider.dart';

class WeatherService {
  static const String baseUrl = 'http://dataservice.accuweather.com';
  static const String apiKey = 'YOUR_ACCUWEATHER_API_KEY'; // Replace with your AccuWeather API key

  Future<Map<String, dynamic>> getWeatherData(ThemeProvider themeProvider) async {
    try {
      String locationKey;
      
      if (themeProvider.useManualLocation && themeProvider.manualLocation.isNotEmpty) {
        // Get location key from city name
        final locationResponse = await http.get(
          Uri.parse(
            '$baseUrl/locations/v1/cities/search?apikey=$apiKey&q=${themeProvider.manualLocation}',
          ),
        );

        if (locationResponse.statusCode == 200) {
          final locations = json.decode(locationResponse.body);
          if (locations.isEmpty) {
            throw Exception('Location not found');
          }
          locationKey = locations[0]['Key'];
        } else {
          throw Exception('Failed to get location key');
        }
      } else {
        // Get location key from coordinates
        final position = await Geolocator.getCurrentPosition(
          desiredAccuracy: LocationAccuracy.high,
        );
        
        final locationResponse = await http.get(
          Uri.parse(
            '$baseUrl/locations/v1/cities/geoposition/search?apikey=$apiKey&q=${position.latitude},${position.longitude}',
          ),
        );

        if (locationResponse.statusCode == 200) {
          final location = json.decode(locationResponse.body);
          locationKey = location['Key'];
        } else {
          throw Exception('Failed to get location key');
        }
      }

      // Get current conditions
      final weatherResponse = await http.get(
        Uri.parse(
          '$baseUrl/currentconditions/v1/$locationKey?apikey=$apiKey&details=true',
        ),
      );

      if (weatherResponse.statusCode == 200) {
        final data = json.decode(weatherResponse.body)[0];
        return {
          'temperature': '${data['Temperature']['Metric']['Value'].round()}°C',
          'condition': data['WeatherText'],
          'location': themeProvider.useManualLocation ? themeProvider.manualLocation : data['LocalizedName'],
          'humidity': '${data['RelativeHumidity']}%',
          'windSpeed': '${data['Wind']['Speed']['Metric']['Value'].round()} km/h',
          'feelsLike': '${data['RealFeelTemperature']['Metric']['Value'].round()}°C',
        };
      } else {
        throw Exception('Failed to load weather data');
      }
    } catch (e) {
      throw Exception('Error: ${e.toString()}');
    }
  }

  Future<Map<String, double>> _getCoordinatesFromLocation(String location) async {
    try {
      final response = await http.get(
        Uri.parse(
          'https://nominatim.openstreetmap.org/search?format=json&q=$location',
        ),
        headers: {
          'User-Agent': 'SmartClock/1.0',
        },
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        if (data.isEmpty) {
          throw Exception('Location not found');
        }

        final lat = double.tryParse(data[0]['lat']);
        final lon = double.tryParse(data[0]['lon']);

        if (lat == null || lon == null) {
          throw Exception('Invalid coordinates');
        }

        return {'lat': lat, 'lon': lon};
      } else {
        throw Exception('Failed to get coordinates');
      }
    } catch (e) {
      throw Exception('Error getting coordinates: ${e.toString()}');
    }
  }

  Map<String, dynamic> _parseWeatherData(String responseBody) {
    final data = json.decode(responseBody);
    
    // Extract current weather data
    final current = data['current'];
    final location = data['location'];
    
    return {
      'temperature': '${current['temp_c'].round()}°C',
      'condition': _getWeatherCondition(current['condition']['text']),
      'location': location['name'],
      'humidity': '${current['humidity']}%',
      'windSpeed': '${current['wind_kph'].round()} km/h',
      'feelsLike': '${current['feelslike_c'].round()}°C',
      'pressure': '${current['pressure_mb']} hPa',
      'visibility': '${current['vis_km']} km',
      'uvIndex': current['uv'].toString(),
      'lastUpdated': _formatDateTime(location['localtime']),
    };
  }

  Future<Position> _getCurrentLocation() async {
    bool serviceEnabled;
    LocationPermission permission;

    // Check if location services are enabled
    serviceEnabled = await Geolocator.isLocationServiceEnabled();
    if (!serviceEnabled) {
      throw Exception('Location services are disabled. Please enable them to get weather data.');
    }

    // Check and request location permissions
    permission = await Geolocator.checkPermission();
    if (permission == LocationPermission.denied) {
      permission = await Geolocator.requestPermission();
      if (permission == LocationPermission.denied) {
        throw Exception('Location permissions are denied. Please grant location permission to get weather data.');
      }
    }

    if (permission == LocationPermission.deniedForever) {
      throw Exception(
        'Location permissions are permanently denied. Please enable them in your device settings.',
      );
    }

    // Get current position with timeout
    try {
      return await Geolocator.getCurrentPosition(
        desiredAccuracy: LocationAccuracy.high,
        timeLimit: const Duration(seconds: 10),
      );
    } catch (e) {
      throw Exception('Failed to get current location: $e');
    }
  }

  String _getWeatherCondition(String condition) {
    final conditionLower = condition.toLowerCase();
    
    // Clear conditions
    if (conditionLower.contains('sunny') || 
        conditionLower.contains('clear')) {
      return 'sunny';
    }
    
    // Cloudy conditions
    if (conditionLower.contains('cloud') || 
        conditionLower.contains('overcast')) {
      return 'cloudy';
    }
    
    // Rain conditions
    if (conditionLower.contains('rain') || 
        conditionLower.contains('shower')) {
      return 'rainy';
    }
    
    // Snow conditions
    if (conditionLower.contains('snow') || 
        conditionLower.contains('sleet')) {
      return 'snowy';
    }
    
    // Storm conditions
    if (conditionLower.contains('thunder') || 
        conditionLower.contains('storm')) {
      return 'stormy';
    }
    
    // Drizzle conditions
    if (conditionLower.contains('drizzle')) {
      return 'drizzly';
    }
    
    // Foggy conditions
    if (conditionLower.contains('mist') || 
        conditionLower.contains('fog') || 
        conditionLower.contains('haze') || 
        conditionLower.contains('smoke') || 
        conditionLower.contains('dust') || 
        conditionLower.contains('sand') || 
        conditionLower.contains('ash') || 
        conditionLower.contains('squall') || 
        conditionLower.contains('tornado')) {
      return 'foggy';
    }
    
    return 'unknown';
  }

  String _formatDateTime(String dateTimeStr) {
    try {
      final dateTime = DateTime.parse(dateTimeStr);
      return '${dateTime.hour.toString().padLeft(2, '0')}:${dateTime.minute.toString().padLeft(2, '0')}';
    } catch (e) {
      return 'Unknown';
    }
  }
} 