import 'package:flutter/material.dart';
import '../core/theme/neon_colors.dart';

class ColorProvider extends ChangeNotifier {
  Color _currentColor = NeonColors.neonBlue;
  bool _isRainbowMode = false;
  double _colorSpeed = 1.0;
  int _currentRainbowIndex = 0;
  AnimationController? _controller;

  // Available colors for the user to choose from
  final List<Color> availableColors = [
    // Classic Neon Colors
    NeonColors.neonBlue,
    NeonColors.neonPink,
    NeonColors.neonPurple,
    NeonColors.neonGreen,
    NeonColors.neonYellow,
    NeonColors.neonRed,
    NeonColors.neonCyan,
    NeonColors.neonOrange,
    NeonColors.neonTeal,
    NeonColors.neonLime,

    // Vaporwave Colors
    NeonColors.vaporwavePink,
    NeonColors.vaporwaveBlue,

    // Retrowave Colors
    NeonColors.retroWaveRed,
    NeonColors.retroWavePurple,

    // Pastel Colors
    NeonColors.pastelLavender,
    NeonColors.pastelPink,
    NeonColors.pastelBlue,
    NeonColors.pastelYellow,
    NeonColors.mintGreen,

    // Modern Colors
    NeonColors.modernGold,
    NeonColors.modernSilver,
    NeonColors.modernRose,
    NeonColors.modernSky,
    NeonColors.modernMint,
  ];

  Color get currentColor => _currentColor;
  bool get isRainbowMode => _isRainbowMode;
  double get colorSpeed => _colorSpeed;

  void initialize(TickerProvider vsync) {
    _controller = AnimationController(
      vsync: vsync,
      duration: Duration(milliseconds: (1000 / _colorSpeed).round()),
    )..addListener(_updateRainbowColor);
  }

  void _updateRainbowColor() {
    if (_isRainbowMode) {
      _currentRainbowIndex = (_currentRainbowIndex + 1) % NeonColors.rainbowColors.length;
      _currentColor = NeonColors.rainbowColors[_currentRainbowIndex];
      notifyListeners();
    }
  }

  void toggleRainbowMode() {
    _isRainbowMode = !_isRainbowMode;
    if (_isRainbowMode) {
      _controller?.repeat();
    } else {
      _controller?.stop();
    }
    notifyListeners();
  }

  void setColorSpeed(double speed) {
    _colorSpeed = speed;
    if (_controller != null) {
      _controller!.duration = Duration(milliseconds: (1000 / speed).round());
    }
    notifyListeners();
  }

  void setStaticColor(Color color) {
    _isRainbowMode = false;
    _controller?.stop();
    _currentColor = color;
    notifyListeners();
  }

  @override
  void dispose() {
    _controller?.dispose();
    super.dispose();
  }
}